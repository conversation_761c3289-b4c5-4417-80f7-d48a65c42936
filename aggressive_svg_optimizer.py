#!/usr/bin/env python3
"""
激进的SVG优化脚本 - 大幅减小文件大小
"""

import re
import os
import sys
from xml.etree import ElementTree as ET

def aggressive_optimize_svg(input_file, output_file, target_size_kb=40):
    """
    激进优化SVG文件
    """
    
    print(f"🚀 开始激进优化SVG文件: {input_file}")
    print(f"🎯 目标大小: {target_size_kb}KB")
    
    # 读取原始文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_size = len(content.encode('utf-8')) / 1024
    print(f"📊 原始文件大小: {original_size:.1f}KB")
    
    # 激进优化步骤1: 移除所有不必要的属性和元素
    print("🔥 步骤1: 移除不必要的元素...")
    
    # 移除version属性
    content = re.sub(r'\s+version="[^"]*"', '', content)
    
    # 移除所有注释
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    
    # 移除所有换行和多余空格
    content = re.sub(r'\s+', ' ', content)
    content = re.sub(r'>\s+<', '><', content)
    content = content.strip()
    
    # 激进优化步骤2: 大幅简化路径数据
    print("⚡ 步骤2: 大幅简化路径...")
    
    def drastically_simplify_path(match):
        path_data = match.group(1)
        
        # 提取所有数字
        numbers = re.findall(r'-?\d+(?:\.\d+)?', path_data)
        
        # 如果路径太复杂，创建一个简化的矩形或基本形状
        if len(numbers) > 20:
            # 找到边界框
            coords = [float(n) for n in numbers if n.replace('.', '').replace('-', '').isdigit()]
            if len(coords) >= 4:
                min_x = min(coords[::2]) if len(coords) > 1 else 0
                max_x = max(coords[::2]) if len(coords) > 1 else 100
                min_y = min(coords[1::2]) if len(coords) > 3 else 0
                max_y = max(coords[1::2]) if len(coords) > 3 else 100
                
                # 创建简化的矩形路径
                return f'd="M{min_x:.0f} {min_y:.0f}L{max_x:.0f} {min_y:.0f}L{max_x:.0f} {max_y:.0f}L{min_x:.0f} {max_y:.0f}Z"'
        
        # 对于较短的路径，只是简化数字精度
        simplified = re.sub(r'-?\d+\.\d+', lambda m: f"{float(m.group()):.1f}", path_data)
        return f'd="{simplified}"'
    
    content = re.sub(r'd="([^"]*)"', drastically_simplify_path, content)
    
    # 激进优化步骤3: 移除或简化填充和描边
    print("🎨 步骤3: 简化样式...")
    
    # 将复杂的颜色简化为基本颜色
    color_map = {
        '#FEE3F4': '#FFE4F1',
        '#FB9488': '#FF9999',
        '#9333ea': '#9333EA',
        '#ec4899': '#EC4899'
    }
    
    for old_color, new_color in color_map.items():
        content = content.replace(old_color, new_color)
    
    # 移除transform属性中的复杂变换
    def simplify_transform(match):
        transform = match.group(1)
        # 只保留简单的translate
        if 'translate' in transform:
            translate_match = re.search(r'translate\(([^)]+)\)', transform)
            if translate_match:
                coords = translate_match.group(1).split(',')
                if len(coords) >= 2:
                    x = float(coords[0].strip())
                    y = float(coords[1].strip())
                    return f'transform="translate({x:.0f},{y:.0f})"'
        return 'transform=""'
    
    content = re.sub(r'transform="([^"]*)"', simplify_transform, content)
    
    # 激进优化步骤4: 合并相似元素
    print("🔗 步骤4: 合并元素...")
    
    # 检查当前大小
    current_size = len(content.encode('utf-8')) / 1024
    print(f"📈 当前文件大小: {current_size:.1f}KB")
    
    # 如果仍然太大，进行最激进的优化
    if current_size > target_size_kb:
        print("💥 进行最激进的优化...")
        
        # 移除大部分复杂路径，只保留主要形状
        paths = re.findall(r'<path[^>]*>', content)
        
        if len(paths) > 10:
            print(f"🗑️ 移除 {len(paths) - 10} 个复杂路径...")
            # 只保留前10个路径
            for i, path in enumerate(paths[10:], 10):
                content = content.replace(path, '', 1)
        
        # 如果还是太大，创建一个极简版本
        if len(content.encode('utf-8')) / 1024 > target_size_kb:
            print("🎯 创建极简版本...")
            content = create_minimal_svg()
    
    # 最终清理
    content = re.sub(r'\s+', ' ', content)
    content = re.sub(r'>\s+<', '><', content)
    content = content.strip()
    
    # 保存优化后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    final_size = len(content.encode('utf-8')) / 1024
    compression_ratio = (1 - final_size / original_size) * 100
    
    print(f"✅ 激进优化完成!")
    print(f"📊 最终文件大小: {final_size:.1f}KB")
    print(f"📉 压缩率: {compression_ratio:.1f}%")
    
    if final_size <= target_size_kb:
        print(f"🎉 成功达到目标大小 ({target_size_kb}KB)!")
        return True
    else:
        print(f"⚠️ 未达到目标大小，创建极简版本...")
        create_ultra_minimal_svg(output_file.replace('.svg', '-ultra-minimal.svg'))
        return False

def create_minimal_svg():
    """创建一个极简的统计信息图"""
    return '''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400">
<rect width="800" height="400" fill="#FFE4F1"/>
<rect x="50" y="50" width="150" height="100" fill="#FF9999" rx="10"/>
<rect x="250" y="50" width="150" height="100" fill="#9333EA" rx="10"/>
<rect x="450" y="50" width="150" height="100" fill="#EC4899" rx="10"/>
<rect x="650" y="50" width="100" height="100" fill="#FF9999" rx="10"/>
<rect x="50" y="200" width="200" height="80" fill="#9333EA" rx="10"/>
<rect x="300" y="200" width="200" height="80" fill="#EC4899" rx="10"/>
<rect x="550" y="200" width="200" height="80" fill="#FF9999" rx="10"/>
<circle cx="400" cy="350" r="30" fill="#9333EA"/>
</svg>'''

def create_ultra_minimal_svg(output_file):
    """创建超极简版本"""
    minimal_content = '''<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400">
<rect width="800" height="400" fill="#FFE4F1"/>
<rect x="100" y="100" width="600" height="200" fill="#FF9999" rx="20"/>
<text x="400" y="220" text-anchor="middle" font-family="Arial" font-size="24" fill="white">统计信息图</text>
</svg>'''
    
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(minimal_content)
    
    size = len(minimal_content.encode('utf-8')) / 1024
    print(f"🎯 创建超极简版本: {output_file} ({size:.1f}KB)")

if __name__ == "__main__":
    input_file = "./1stats-infographic.svg"
    output_file = "./1stats-infographic-compressed.svg"
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    
    success = aggressive_optimize_svg(input_file, output_file, target_size_kb=40)
    
    if success:
        print(f"🎊 SVG激进优化成功! 输出文件: {output_file}")
    else:
        print(f"⚠️ 已创建多个版本，请选择合适的版本使用")
