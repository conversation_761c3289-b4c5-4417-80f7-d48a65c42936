<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="800" height="400" viewBox="0 0 800 400">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FEE3F4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F8BBD9;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="card1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9333EA;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7C3AED;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="card2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#EC4899;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#DB2777;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="card3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#F59E0B;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#D97706;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="800" height="400" fill="url(#bg)"/>
  
  <!-- 标题区域 -->
  <rect x="50" y="30" width="700" height="60" fill="white" rx="15" opacity="0.9"/>
  <text x="400" y="55" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#1F2937">月经健康统计数据</text>
  <text x="400" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6B7280">Period Health Statistics</text>
  
  <!-- 第一行统计卡片 -->
  <!-- 卡片1: 痛经比例 -->
  <rect x="60" y="120" width="160" height="100" fill="url(#card1)" rx="12"/>
  <text x="140" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">85%</text>
  <text x="140" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">女性经历痛经</text>
  <text x="140" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Experience Period Pain</text>
  <text x="140" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">全球统计数据</text>
  
  <!-- 卡片2: 影响工作学习 -->
  <rect x="240" y="120" width="160" height="100" fill="url(#card2)" rx="12"/>
  <text x="320" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">73%</text>
  <text x="320" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">影响工作学习</text>
  <text x="320" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Affects Work/Study</text>
  <text x="320" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">生产力影响</text>
  
  <!-- 卡片3: 寻求医疗帮助 -->
  <rect x="420" y="120" width="160" height="100" fill="url(#card3)" rx="12"/>
  <text x="500" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">42%</text>
  <text x="500" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">寻求医疗帮助</text>
  <text x="500" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Seek Medical Help</text>
  <text x="500" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">就医比例</text>
  
  <!-- 卡片4: 使用止痛药 -->
  <rect x="600" y="120" width="140" height="100" fill="url(#card1)" rx="12"/>
  <text x="670" y="145" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="white">91%</text>
  <text x="670" y="165" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">使用止痛药</text>
  <text x="670" y="180" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Use Painkillers</text>
  <text x="670" y="200" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="rgba(255,255,255,0.8)">缓解方式</text>
  
  <!-- 第二行统计卡片 -->
  <!-- 卡片5: 平均疼痛持续时间 -->
  <rect x="80" y="250" width="180" height="80" fill="url(#card2)" rx="12"/>
  <text x="170" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white">2.3天</text>
  <text x="170" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">平均疼痛持续时间</text>
  <text x="170" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Average Pain Duration</text>
  
  <!-- 卡片6: 疼痛等级 -->
  <rect x="280" y="250" width="180" height="80" fill="url(#card3)" rx="12"/>
  <text x="370" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white">6.2/10</text>
  <text x="370" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">平均疼痛等级</text>
  <text x="370" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Average Pain Level</text>
  
  <!-- 卡片7: 年龄分布 -->
  <rect x="480" y="250" width="180" height="80" fill="url(#card1)" rx="12"/>
  <text x="570" y="275" text-anchor="middle" font-family="Arial, sans-serif" font-size="28" font-weight="bold" fill="white">15-45岁</text>
  <text x="570" y="295" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">主要影响年龄段</text>
  <text x="570" y="310" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white">Primary Age Range</text>
  
  <!-- 底部信息 -->
  <rect x="50" y="350" width="700" height="30" fill="white" rx="8" opacity="0.8"/>
  <text x="400" y="370" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#6B7280">数据来源：全球女性健康调查 | Data Source: Global Women's Health Survey</text>
  
  <!-- 装饰元素 -->
  <circle cx="750" cy="50" r="8" fill="#EC4899" opacity="0.6"/>
  <circle cx="730" cy="70" r="5" fill="#9333EA" opacity="0.6"/>
  <circle cx="770" cy="80" r="6" fill="#F59E0B" opacity="0.6"/>
  
  <circle cx="50" cy="350" r="6" fill="#EC4899" opacity="0.4"/>
  <circle cx="30" cy="370" r="4" fill="#9333EA" opacity="0.4"/>
  <circle cx="70" cy="380" r="5" fill="#F59E0B" opacity="0.4"/>
</svg>