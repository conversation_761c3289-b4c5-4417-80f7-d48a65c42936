#!/usr/bin/env python3
"""
SVG优化脚本 - 将SVG文件压缩到指定大小以下
"""

import re
import os
import sys

def optimize_svg(input_file, output_file, target_size_kb=40):
    """
    优化SVG文件，减小文件大小
    
    Args:
        input_file: 输入SVG文件路径
        output_file: 输出SVG文件路径
        target_size_kb: 目标文件大小（KB）
    """
    
    print(f"🔧 开始优化SVG文件: {input_file}")
    print(f"🎯 目标大小: {target_size_kb}KB")
    
    # 读取原始文件
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    original_size = len(content.encode('utf-8')) / 1024
    print(f"📊 原始文件大小: {original_size:.1f}KB")
    
    # 优化步骤1: 移除注释和多余空白
    print("🧹 步骤1: 清理注释和空白...")
    content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
    content = re.sub(r'\s+', ' ', content)
    content = re.sub(r'>\s+<', '><', content)
    
    # 优化步骤2: 简化数值精度
    print("🔢 步骤2: 简化数值精度...")
    def round_numbers(match):
        num = float(match.group(0))
        if abs(num) < 0.01:
            return '0'
        elif abs(num - round(num)) < 0.01:
            return str(int(round(num)))
        else:
            return f"{num:.2f}".rstrip('0').rstrip('.')
    
    content = re.sub(r'-?\d+\.\d+', round_numbers, content)
    
    # 优化步骤3: 移除默认属性
    print("✂️ 步骤3: 移除默认属性...")
    content = re.sub(r'\s+fill="none"', '', content)
    content = re.sub(r'\s+stroke="none"', '', content)
    content = re.sub(r'\s+fill-opacity="1"', '', content)
    content = re.sub(r'\s+stroke-opacity="1"', '', content)
    
    # 优化步骤4: 简化路径数据
    print("🛤️ 步骤4: 简化路径数据...")
    def simplify_path(match):
        path_data = match.group(1)
        # 移除多余的空格和逗号
        path_data = re.sub(r'\s*,\s*', ',', path_data)
        path_data = re.sub(r'\s+', ' ', path_data)
        # 简化连续的相同命令
        path_data = re.sub(r'([MLHVCSQTAZ])\s*\1', r'\1', path_data, flags=re.IGNORECASE)
        return f'd="{path_data.strip()}"'
    
    content = re.sub(r'd="([^"]*)"', simplify_path, content)
    
    # 优化步骤5: 合并相似的元素（如果可能）
    print("🔗 步骤5: 优化元素结构...")
    
    # 检查当前大小
    current_size = len(content.encode('utf-8')) / 1024
    print(f"📈 当前文件大小: {current_size:.1f}KB")
    
    # 如果仍然太大，进行更激进的优化
    if current_size > target_size_kb:
        print("⚡ 进行更激进的优化...")
        
        # 移除transform属性中的多余精度
        def simplify_transform(match):
            transform = match.group(1)
            transform = re.sub(r'-?\d+\.\d+', round_numbers, transform)
            return f'transform="{transform}"'
        
        content = re.sub(r'transform="([^"]*)"', simplify_transform, content)
        
        # 如果还是太大，考虑移除一些复杂的路径
        if len(content.encode('utf-8')) / 1024 > target_size_kb * 1.5:
            print("🎨 简化复杂路径...")
            # 找到最长的路径并简化
            paths = re.findall(r'd="([^"]*)"', content)
            if paths:
                # 按长度排序，简化最长的路径
                paths_with_length = [(p, len(p)) for p in paths]
                paths_with_length.sort(key=lambda x: x[1], reverse=True)
                
                # 简化前几个最长的路径
                for i, (path, length) in enumerate(paths_with_length[:3]):
                    if length > 1000:  # 只简化很长的路径
                        # 减少路径点的数量
                        simplified = simplify_long_path(path)
                        content = content.replace(f'd="{path}"', f'd="{simplified}"', 1)
    
    # 最终清理
    content = content.strip()
    
    # 保存优化后的文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    final_size = len(content.encode('utf-8')) / 1024
    compression_ratio = (1 - final_size / original_size) * 100
    
    print(f"✅ 优化完成!")
    print(f"📊 最终文件大小: {final_size:.1f}KB")
    print(f"📉 压缩率: {compression_ratio:.1f}%")
    
    if final_size <= target_size_kb:
        print(f"🎉 成功达到目标大小 ({target_size_kb}KB)!")
        return True
    else:
        print(f"⚠️ 未达到目标大小，还需要进一步优化")
        return False

def simplify_long_path(path_data):
    """简化长路径数据"""
    # 移除一些中间点，保持路径的大致形状
    commands = re.findall(r'[MLHVCSQTAZ][^MLHVCSQTAZ]*', path_data, re.IGNORECASE)
    
    if len(commands) > 50:  # 如果命令太多，减少一些
        # 保留每第二个命令（简单的抽样）
        simplified_commands = []
        for i, cmd in enumerate(commands):
            if i == 0 or i == len(commands) - 1 or i % 2 == 0:
                simplified_commands.append(cmd)
        return ' '.join(simplified_commands)
    
    return path_data

if __name__ == "__main__":
    input_file = "./1stats-infographic.svg"
    output_file = "./1stats-infographic-optimized.svg"
    
    if not os.path.exists(input_file):
        print(f"❌ 错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    
    success = optimize_svg(input_file, output_file, target_size_kb=40)
    
    if success:
        print(f"🎊 SVG优化成功! 输出文件: {output_file}")
    else:
        print(f"⚠️ SVG已优化但未达到目标大小，输出文件: {output_file}")
        print("💡 建议: 可能需要手动进一步简化图形内容")
