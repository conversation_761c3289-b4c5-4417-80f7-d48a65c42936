# Period Hub GitHub Pages 部署工作流
name: Deploy Period Hub to GitHub Pages

on:
  # 推送到 main 分支时触发
  push:
    branches: [ main ]
  
  # 允许手动触发
  workflow_dispatch:

# 设置 GITHUB_TOKEN 权限
permissions:
  contents: read
  pages: write
  id-token: write

# 确保只有一个部署任务同时运行
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  # 构建任务
  build:
    runs-on: ubuntu-latest
    
    steps:
      # 1. 检出代码
      - name: 📥 检出代码
        uses: actions/checkout@v4
        
      # 2. 设置 Node.js 环境
      - name: 🔧 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      # 3. 安装依赖
      - name: 📦 安装依赖
        run: npm ci
        
      # 4. 构建项目 (静态导出)
      - name: 🏗️ 构建 Next.js 项目 (静态导出)
        run: |
          npm run build
        env:
          # 生产环境变量
          NODE_ENV: production
          DEPLOY_TARGET: github
          NEXT_PUBLIC_BASE_URL: https://periodhub.health

      # 4.5. 添加必要文件
      - name: 📄 添加 GitHub Pages 必要文件
        run: |
          # 添加 .nojekyll 文件 (禁用 Jekyll)
          touch out/.nojekyll
          # 添加 CNAME 文件 (自定义域名)
          echo 'periodhub.health' > out/CNAME
          
      # 5. 设置 GitHub Pages
      - name: ⚙️ 设置 GitHub Pages
        uses: actions/configure-pages@v4
        
      # 6. 上传构建产物
      - name: 📤 上传构建产物
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./out

  # 部署任务
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    
    steps:
      # 部署到 GitHub Pages
      - name: 🚀 部署到 GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4
