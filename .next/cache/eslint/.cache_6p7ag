[{"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx": "1", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/page.tsx": "2", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/assessment/page.tsx": "3", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/cultural-charms/page.tsx": "4", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/framework-demo/page.tsx": "5", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/global-perspectives/page.tsx": "6", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx": "7", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/medical-care/page.tsx": "8", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/myths-facts/page.tsx": "9", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/page.tsx": "10", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx": "11", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/understanding-pain/page.tsx": "12", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/immediate-relief/page.tsx": "13", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx": "14", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx": "15", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PainTrackerTool.tsx": "16", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx": "17", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx": "18", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx": "19", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/pain-tracker/components/PainEntryForm.tsx": "20", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/HealthDataDashboard.tsx": "21", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/LoadingSpinner.tsx": "22", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/NotificationContainer.tsx": "23", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/constants/index.ts": "24", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/assessmentQuestions.ts": "25", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionQuestions.ts": "26", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionRecommendations.ts": "27", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionTypes.ts": "28", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/menstrualPainRecommendations.ts": "29", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts": "30", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts": "31", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useNotifications.ts": "32", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts": "33", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts": "34", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/stores/healthDataStore.ts": "35", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/constitution.ts": "36", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/index.ts": "37", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/utils/index.ts": "38", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/test-assessment.tsx": "39", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx": "40", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/medical-disclaimer/page.tsx": "41", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/natural-therapies/page.tsx": "42", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/page.tsx": "43", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx": "44", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/privacy-policy/page.tsx": "45", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/commute/page.tsx": "46", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/emergency-kit/page.tsx": "47", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/exercise/page.tsx": "48", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/lifeStages/page.tsx": "49", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/office/page.tsx": "50", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/page.tsx": "51", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx": "52", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/social/page.tsx": "53", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/campus-guide/page.tsx": "54", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/communication-guide/page.tsx": "55", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/development-pain/page.tsx": "56", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/emotional-support/page.tsx": "57", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/page.tsx": "58", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/terms-of-service/page.tsx": "59", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/icon.tsx": "60", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx": "61", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx": "62", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/page.tsx": "63", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/robots.ts": "64", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/sitemap.ts": "65", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx": "66", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-symptom-assessment/page.tsx": "67", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ArticleInteractions.tsx": "68", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx": "69", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ClientIframe.tsx": "70", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ClientImage.tsx": "71", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/DownloadButton.tsx": "72", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/EmbeddedPainAssessment.tsx": "73", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx": "74", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx": "75", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx": "76", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContent.tsx": "77", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContentSimple.tsx": "78", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDInteractive.tsx": "79", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NavigationTabs.tsx": "80", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ReadingProgress.tsx": "81", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/SearchBox.tsx": "82", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/StructuredData.tsx": "83", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/TableOfContents.tsx": "84", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ToolsCollectionButton.tsx": "85", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/UserSuccessStories.tsx": "86", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx": "87", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/DataVisualization.tsx": "88", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx": "89", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/FormSystem.tsx": "90", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx": "91", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/SearchSystem.tsx": "92", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx": "93", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/api/client.ts": "94", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/articles.ts": "95", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/cache/manager.ts": "96", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/performance/monitor.ts": "97", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/stores/appStore.ts": "98", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/translation-utils.ts": "99"}, {"size": 19682, "mtime": 1749469774935, "results": "100", "hashOfConfig": "101"}, {"size": 55444, "mtime": 1749655986131, "results": "102", "hashOfConfig": "101"}, {"size": 11615, "mtime": 1749038859000, "results": "103", "hashOfConfig": "101"}, {"size": 4901, "mtime": 1749084845000, "results": "104", "hashOfConfig": "101"}, {"size": 8686, "mtime": 1749315129046, "results": "105", "hashOfConfig": "101"}, {"size": 22827, "mtime": 1749415912680, "results": "106", "hashOfConfig": "101"}, {"size": 20602, "mtime": 1749415831414, "results": "107", "hashOfConfig": "101"}, {"size": 20154, "mtime": 1749415938042, "results": "108", "hashOfConfig": "101"}, {"size": 20952, "mtime": 1749415866914, "results": "109", "hashOfConfig": "101"}, {"size": 9688, "mtime": 1749415899453, "results": "110", "hashOfConfig": "101"}, {"size": 23164, "mtime": 1749415924965, "results": "111", "hashOfConfig": "101"}, {"size": 34226, "mtime": 1749415854509, "results": "112", "hashOfConfig": "101"}, {"size": 14743, "mtime": 1749218651798, "results": "113", "hashOfConfig": "101"}, {"size": 25408, "mtime": 1749412994893, "results": "114", "hashOfConfig": "101"}, {"size": 41931, "mtime": 1749524029665, "results": "115", "hashOfConfig": "101"}, {"size": 12750, "mtime": 1749495176542, "results": "116", "hashOfConfig": "101"}, {"size": 8692, "mtime": 1749355480374, "results": "117", "hashOfConfig": "101"}, {"size": 28300, "mtime": 1749567554494, "results": "118", "hashOfConfig": "101"}, {"size": 8203, "mtime": 1749524500858, "results": "119", "hashOfConfig": "101"}, {"size": 19269, "mtime": 1749494552983, "results": "120", "hashOfConfig": "101"}, {"size": 9893, "mtime": 1749404324690, "results": "121", "hashOfConfig": "101"}, {"size": 2148, "mtime": 1748955880000, "results": "122", "hashOfConfig": "101"}, {"size": 4460, "mtime": 1748955849000, "results": "123", "hashOfConfig": "101"}, {"size": 10440, "mtime": 1748955643000, "results": "124", "hashOfConfig": "101"}, {"size": 16080, "mtime": 1749524095877, "results": "125", "hashOfConfig": "101"}, {"size": 22387, "mtime": 1749273564148, "results": "126", "hashOfConfig": "101"}, {"size": 40122, "mtime": 1749269452738, "results": "127", "hashOfConfig": "101"}, {"size": 12627, "mtime": 1749268845517, "results": "128", "hashOfConfig": "101"}, {"size": 55170, "mtime": 1749274738601, "results": "129", "hashOfConfig": "101"}, {"size": 1111, "mtime": 1749309117994, "results": "130", "hashOfConfig": "101"}, {"size": 6699, "mtime": 1749499765509, "results": "131", "hashOfConfig": "101"}, {"size": 2924, "mtime": 1748955759000, "results": "132", "hashOfConfig": "101"}, {"size": 9480, "mtime": 1749494407073, "results": "133", "hashOfConfig": "101"}, {"size": 21494, "mtime": 1749499408128, "results": "134", "hashOfConfig": "101"}, {"size": 6535, "mtime": 1749310456733, "results": "135", "hashOfConfig": "101"}, {"size": 2475, "mtime": 1749273734740, "results": "136", "hashOfConfig": "101"}, {"size": 4374, "mtime": 1748957313000, "results": "137", "hashOfConfig": "101"}, {"size": 8906, "mtime": 1749494858101, "results": "138", "hashOfConfig": "101"}, {"size": 1179, "mtime": 1748958196000, "results": "139", "hashOfConfig": "101"}, {"size": 2794, "mtime": 1749415778407, "results": "140", "hashOfConfig": "101"}, {"size": 10816, "mtime": 1749037948000, "results": "141", "hashOfConfig": "101"}, {"size": 103188, "mtime": 1749533316137, "results": "142", "hashOfConfig": "101"}, {"size": 30759, "mtime": 1749652368171, "results": "143", "hashOfConfig": "101"}, {"size": 13892, "mtime": 1749038922000, "results": "144", "hashOfConfig": "101"}, {"size": 10631, "mtime": 1749227891908, "results": "145", "hashOfConfig": "101"}, {"size": 27395, "mtime": 1749275511468, "results": "146", "hashOfConfig": "101"}, {"size": 30287, "mtime": 1749282967869, "results": "147", "hashOfConfig": "101"}, {"size": 17750, "mtime": 1748889835000, "results": "148", "hashOfConfig": "101"}, {"size": 21975, "mtime": 1749063614000, "results": "149", "hashOfConfig": "101"}, {"size": 23191, "mtime": 1749277795458, "results": "150", "hashOfConfig": "101"}, {"size": 15547, "mtime": 1749282075248, "results": "151", "hashOfConfig": "101"}, {"size": 18571, "mtime": 1748890157000, "results": "152", "hashOfConfig": "101"}, {"size": 21213, "mtime": 1749277815173, "results": "153", "hashOfConfig": "101"}, {"size": 21075, "mtime": 1749281693835, "results": "154", "hashOfConfig": "101"}, {"size": 20005, "mtime": 1749281990142, "results": "155", "hashOfConfig": "101"}, {"size": 19578, "mtime": 1749289181223, "results": "156", "hashOfConfig": "101"}, {"size": 21587, "mtime": 1749281887160, "results": "157", "hashOfConfig": "101"}, {"size": 17538, "mtime": 1749289138432, "results": "158", "hashOfConfig": "101"}, {"size": 13327, "mtime": 1749227906245, "results": "159", "hashOfConfig": "101"}, {"size": 865, "mtime": 1748874187000, "results": "160", "hashOfConfig": "101"}, {"size": 12334, "mtime": 1749637942492, "results": "161", "hashOfConfig": "101"}, {"size": 3233, "mtime": 1749356977988, "results": "162", "hashOfConfig": "101"}, {"size": 136, "mtime": 1749283345657, "results": "163", "hashOfConfig": "101"}, {"size": 915, "mtime": 1749367548101, "results": "164", "hashOfConfig": "101"}, {"size": 2116, "mtime": 1749415843004, "results": "165", "hashOfConfig": "101"}, {"size": 155, "mtime": 1748958216000, "results": "166", "hashOfConfig": "101"}, {"size": 6159, "mtime": 1748959580000, "results": "167", "hashOfConfig": "101"}, {"size": 9495, "mtime": 1749468511856, "results": "168", "hashOfConfig": "101"}, {"size": 7973, "mtime": 1749541741516, "results": "169", "hashOfConfig": "101"}, {"size": 1246, "mtime": 1749652451038, "results": "170", "hashOfConfig": "101"}, {"size": 1162, "mtime": 1749652406784, "results": "171", "hashOfConfig": "101"}, {"size": 1489, "mtime": 1749523267650, "results": "172", "hashOfConfig": "101"}, {"size": 6382, "mtime": 1749541777078, "results": "173", "hashOfConfig": "101"}, {"size": 7210, "mtime": 1749542233356, "results": "174", "hashOfConfig": "101"}, {"size": 7622, "mtime": 1749541865795, "results": "175", "hashOfConfig": "101"}, {"size": 1148, "mtime": 1749283175464, "results": "176", "hashOfConfig": "101"}, {"size": 37256, "mtime": 1749412453669, "results": "177", "hashOfConfig": "101"}, {"size": 9086, "mtime": 1749364442896, "results": "178", "hashOfConfig": "101"}, {"size": 1269, "mtime": 1749226398521, "results": "179", "hashOfConfig": "101"}, {"size": 1029, "mtime": 1749541979442, "results": "180", "hashOfConfig": "101"}, {"size": 2122, "mtime": 1749468532179, "results": "181", "hashOfConfig": "101"}, {"size": 8586, "mtime": 1749620497053, "results": "182", "hashOfConfig": "101"}, {"size": 3404, "mtime": 1749367729054, "results": "183", "hashOfConfig": "101"}, {"size": 4261, "mtime": 1749468554948, "results": "184", "hashOfConfig": "101"}, {"size": 136, "mtime": 1749523920348, "results": "185", "hashOfConfig": "101"}, {"size": 4291, "mtime": 1749541803986, "results": "186", "hashOfConfig": "101"}, {"size": 5945, "mtime": 1749312567487, "results": "187", "hashOfConfig": "101"}, {"size": 9911, "mtime": 1749314039693, "results": "188", "hashOfConfig": "101"}, {"size": 9835, "mtime": 1749312268063, "results": "189", "hashOfConfig": "101"}, {"size": 11549, "mtime": 1749412398660, "results": "190", "hashOfConfig": "101"}, {"size": 8357, "mtime": 1749312537574, "results": "191", "hashOfConfig": "101"}, {"size": 11037, "mtime": 1749313013725, "results": "192", "hashOfConfig": "101"}, {"size": 7422, "mtime": 1749312497520, "results": "193", "hashOfConfig": "101"}, {"size": 9337, "mtime": 1749312226101, "results": "194", "hashOfConfig": "101"}, {"size": 5684, "mtime": 1749129831000, "results": "195", "hashOfConfig": "101"}, {"size": 9006, "mtime": 1749412689751, "results": "196", "hashOfConfig": "101"}, {"size": 8738, "mtime": 1749412718965, "results": "197", "hashOfConfig": "101"}, {"size": 7504, "mtime": 1749312117614, "results": "198", "hashOfConfig": "101"}, {"size": 3725, "mtime": 1749496816580, "results": "199", "hashOfConfig": "101"}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "15nla8l", {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx", ["497"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/page.tsx", ["498", "499", "500", "501", "502", "503"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/assessment/page.tsx", ["504"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/cultural-charms/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/framework-demo/page.tsx", ["505", "506", "507"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/global-perspectives/page.tsx", ["508", "509"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx", ["510", "511"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/medical-care/page.tsx", ["512", "513"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/myths-facts/page.tsx", ["514", "515"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/page.tsx", ["516", "517"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/understanding-pain/page.tsx", ["518", "519"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/immediate-relief/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx", ["520"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", ["521", "522", "523", "524", "525", "526", "527", "528", "529", "530"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PainTrackerTool.tsx", ["531", "532", "533", "534", "535", "536", "537", "538"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", ["539", "540", "541", "542", "543", "544", "545", "546", "547"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/pain-tracker/components/PainEntryForm.tsx", ["548", "549", "550", "551"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/HealthDataDashboard.tsx", ["552"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/NotificationContainer.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/constants/index.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/assessmentQuestions.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionQuestions.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionRecommendations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionTypes.ts", ["553"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/menstrualPainRecommendations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts", ["554", "555", "556"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useNotifications.ts", ["557", "558", "559"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", ["560", "561"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", ["562", "563", "564", "565"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/stores/healthDataStore.ts", ["566", "567", "568", "569", "570"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/constitution.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/index.ts", ["571"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/utils/index.ts", ["572", "573"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/test-assessment.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx", ["574", "575"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/medical-disclaimer/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/natural-therapies/page.tsx", ["576", "577", "578", "579"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/page.tsx", ["580"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx", ["581", "582"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/privacy-policy/page.tsx", ["583"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/commute/page.tsx", ["584"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/emergency-kit/page.tsx", ["585"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/exercise/page.tsx", ["586"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/lifeStages/page.tsx", ["587", "588", "589"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/office/page.tsx", ["590", "591", "592"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx", ["593", "594"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/social/page.tsx", ["595", "596", "597"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/campus-guide/page.tsx", ["598", "599"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/communication-guide/page.tsx", ["600", "601", "602"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/development-pain/page.tsx", ["603", "604"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/emotional-support/page.tsx", ["605", "606", "607", "608"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/page.tsx", ["609", "610"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/terms-of-service/page.tsx", ["611"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/icon.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/robots.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/sitemap.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-symptom-assessment/page.tsx", ["612", "613"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ArticleInteractions.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx", ["614"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ClientIframe.tsx", ["615"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ClientImage.tsx", ["616"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/DownloadButton.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/EmbeddedPainAssessment.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContent.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContentSimple.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDInteractive.tsx", ["617", "618"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NavigationTabs.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ReadingProgress.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/SearchBox.tsx", ["619"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/StructuredData.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/TableOfContents.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ToolsCollectionButton.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/UserSuccessStories.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx", ["620", "621", "622"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/DataVisualization.tsx", ["623", "624"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx", ["625"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/FormSystem.tsx", ["626", "627", "628", "629", "630", "631", "632", "633", "634"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/SearchSystem.tsx", ["635", "636", "637", "638", "639", "640", "641", "642"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx", ["643"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/api/client.ts", ["644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/articles.ts", ["662", "663", "664", "665"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/cache/manager.ts", ["666", "667", "668", "669"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/performance/monitor.ts", ["670", "671"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/stores/appStore.ts", ["672", "673"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/translation-utils.ts", ["674", "675"], [], {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "677", "line": 3, "column": 10, "nodeType": null, "messageId": "678", "endLine": 3, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "679", "line": 9, "column": 8, "nodeType": null, "messageId": "678", "endLine": 9, "endColumn": 24}, {"ruleId": "680", "severity": 1, "message": "681", "line": 89, "column": 29, "nodeType": "682", "messageId": "683", "endLine": 89, "endColumn": 32, "suggestions": "684"}, {"ruleId": "676", "severity": 1, "message": "685", "line": 821, "column": 21, "nodeType": null, "messageId": "678", "endLine": 821, "endColumn": 34}, {"ruleId": "676", "severity": 1, "message": "685", "line": 890, "column": 21, "nodeType": null, "messageId": "678", "endLine": 890, "endColumn": 34}, {"ruleId": "676", "severity": 1, "message": "685", "line": 959, "column": 21, "nodeType": null, "messageId": "678", "endLine": 959, "endColumn": 34}, {"ruleId": "680", "severity": 1, "message": "681", "line": 48, "column": 69, "nodeType": "682", "messageId": "683", "endLine": 48, "endColumn": 72, "suggestions": "686"}, {"ruleId": "676", "severity": 1, "message": "687", "line": 11, "column": 3, "nodeType": null, "messageId": "678", "endLine": 11, "endColumn": 11}, {"ruleId": "676", "severity": 1, "message": "688", "line": 19, "column": 9, "nodeType": null, "messageId": "678", "endLine": 19, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "689", "line": 22, "column": 10, "nodeType": null, "messageId": "678", "endLine": 22, "endColumn": 18}, {"ruleId": "676", "severity": 1, "message": "690", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "690", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "690", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "690", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "688", "line": 33, "column": 9, "nodeType": null, "messageId": "678", "endLine": 33, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "691", "line": 34, "column": 9, "nodeType": null, "messageId": "678", "endLine": 34, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "677", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "691", "line": 37, "column": 9, "nodeType": null, "messageId": "678", "endLine": 37, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "688", "line": 485, "column": 9, "nodeType": null, "messageId": "678", "endLine": 485, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "692", "line": 55, "column": 5, "nodeType": null, "messageId": "678", "endLine": 55, "endColumn": 15}, {"ruleId": "676", "severity": 1, "message": "693", "line": 108, "column": 11, "nodeType": null, "messageId": "678", "endLine": 108, "endColumn": 26}, {"ruleId": "680", "severity": 1, "message": "681", "line": 186, "column": 91, "nodeType": "682", "messageId": "683", "endLine": 186, "endColumn": 94, "suggestions": "694"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 187, "column": 51, "nodeType": "682", "messageId": "683", "endLine": 187, "endColumn": 54, "suggestions": "695"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 192, "column": 95, "nodeType": "682", "messageId": "683", "endLine": 192, "endColumn": 98, "suggestions": "696"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 193, "column": 55, "nodeType": "682", "messageId": "683", "endLine": 193, "endColumn": 58, "suggestions": "697"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 538, "column": 84, "nodeType": "682", "messageId": "683", "endLine": 538, "endColumn": 87, "suggestions": "698"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 553, "column": 88, "nodeType": "682", "messageId": "683", "endLine": 553, "endColumn": 91, "suggestions": "699"}, {"ruleId": "700", "severity": 1, "message": "701", "line": 697, "column": 27, "nodeType": "702", "messageId": "703", "suggestions": "704"}, {"ruleId": "700", "severity": 1, "message": "701", "line": 697, "column": 46, "nodeType": "702", "messageId": "703", "suggestions": "705"}, {"ruleId": "676", "severity": 1, "message": "687", "line": 5, "column": 43, "nodeType": null, "messageId": "678", "endLine": 5, "endColumn": 51}, {"ruleId": "680", "severity": 1, "message": "681", "line": 20, "column": 9, "nodeType": "682", "messageId": "683", "endLine": 20, "endColumn": 12, "suggestions": "706"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 47, "column": 39, "nodeType": "682", "messageId": "683", "endLine": 47, "endColumn": 42, "suggestions": "707"}, {"ruleId": "676", "severity": 1, "message": "708", "line": 61, "column": 14, "nodeType": null, "messageId": "678", "endLine": 61, "endColumn": 19}, {"ruleId": "680", "severity": 1, "message": "681", "line": 72, "column": 40, "nodeType": "682", "messageId": "683", "endLine": 72, "endColumn": 43, "suggestions": "709"}, {"ruleId": "676", "severity": 1, "message": "708", "line": 89, "column": 14, "nodeType": null, "messageId": "678", "endLine": 89, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "708", "line": 115, "column": 16, "nodeType": null, "messageId": "678", "endLine": 115, "endColumn": 21}, {"ruleId": "680", "severity": 1, "message": "681", "line": 124, "column": 34, "nodeType": "682", "messageId": "683", "endLine": 124, "endColumn": 37, "suggestions": "710"}, {"ruleId": "676", "severity": 1, "message": "690", "line": 4, "column": 10, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "711", "line": 11, "column": 3, "nodeType": null, "messageId": "678", "endLine": 11, "endColumn": 11}, {"ruleId": "676", "severity": 1, "message": "712", "line": 19, "column": 8, "nodeType": null, "messageId": "678", "endLine": 19, "endColumn": 22}, {"ruleId": "680", "severity": 1, "message": "681", "line": 29, "column": 73, "nodeType": "682", "messageId": "683", "endLine": 29, "endColumn": 76, "suggestions": "713"}, {"ruleId": "676", "severity": 1, "message": "692", "line": 37, "column": 5, "nodeType": null, "messageId": "678", "endLine": 37, "endColumn": 15}, {"ruleId": "676", "severity": 1, "message": "714", "line": 39, "column": 5, "nodeType": null, "messageId": "678", "endLine": 39, "endColumn": 14}, {"ruleId": "680", "severity": 1, "message": "681", "line": 66, "column": 38, "nodeType": "682", "messageId": "683", "endLine": 66, "endColumn": 41, "suggestions": "715"}, {"ruleId": "676", "severity": 1, "message": "693", "line": 454, "column": 35, "nodeType": null, "messageId": "678", "endLine": 454, "endColumn": 50}, {"ruleId": "680", "severity": 1, "message": "681", "line": 464, "column": 61, "nodeType": "682", "messageId": "683", "endLine": 464, "endColumn": 64, "suggestions": "716"}, {"ruleId": "676", "severity": 1, "message": "717", "line": 3, "column": 27, "nodeType": null, "messageId": "678", "endLine": 3, "endColumn": 36}, {"ruleId": "676", "severity": 1, "message": "718", "line": 16, "column": 27, "nodeType": null, "messageId": "678", "endLine": 16, "endColumn": 44}, {"ruleId": "676", "severity": 1, "message": "719", "line": 50, "column": 10, "nodeType": null, "messageId": "678", "endLine": 50, "endColumn": 17}, {"ruleId": "680", "severity": 1, "message": "681", "line": 61, "column": 69, "nodeType": "682", "messageId": "683", "endLine": 61, "endColumn": 72, "suggestions": "720"}, {"ruleId": "676", "severity": 1, "message": "688", "line": 22, "column": 11, "nodeType": null, "messageId": "678", "endLine": 22, "endColumn": 12}, {"ruleId": "676", "severity": 1, "message": "721", "line": 1, "column": 32, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 59}, {"ruleId": "676", "severity": 1, "message": "722", "line": 13, "column": 10, "nodeType": null, "messageId": "678", "endLine": 13, "endColumn": 30}, {"ruleId": "723", "severity": 1, "message": "724", "line": 50, "column": 9, "nodeType": "725", "endLine": 50, "endColumn": 115}, {"ruleId": "676", "severity": 1, "message": "726", "line": 179, "column": 14, "nodeType": null, "messageId": "678", "endLine": 179, "endColumn": 17}, {"ruleId": "676", "severity": 1, "message": "717", "line": 3, "column": 33, "nodeType": null, "messageId": "678", "endLine": 3, "endColumn": 42}, {"ruleId": "676", "severity": 1, "message": "727", "line": 4, "column": 24, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 42}, {"ruleId": "723", "severity": 1, "message": "728", "line": 38, "column": 6, "nodeType": "729", "endLine": 38, "endColumn": 8, "suggestions": "730"}, {"ruleId": "676", "severity": 1, "message": "731", "line": 13, "column": 10, "nodeType": null, "messageId": "678", "endLine": 13, "endColumn": 22}, {"ruleId": "680", "severity": 1, "message": "681", "line": 107, "column": 22, "nodeType": "682", "messageId": "683", "endLine": 107, "endColumn": 25, "suggestions": "732"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 31, "column": 28, "nodeType": "682", "messageId": "683", "endLine": 31, "endColumn": 31, "suggestions": "733"}, {"ruleId": "723", "severity": 1, "message": "734", "line": 67, "column": 9, "nodeType": "725", "endLine": 67, "endColumn": 111}, {"ruleId": "680", "severity": 1, "message": "681", "line": 155, "column": 116, "nodeType": "682", "messageId": "683", "endLine": 155, "endColumn": 119, "suggestions": "735"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 342, "column": 47, "nodeType": "682", "messageId": "683", "endLine": 342, "endColumn": 50, "suggestions": "736"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 23, "column": 12, "nodeType": "682", "messageId": "683", "endLine": 23, "endColumn": 15, "suggestions": "737"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 25, "column": 16, "nodeType": "682", "messageId": "683", "endLine": 25, "endColumn": 19, "suggestions": "738"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 26, "column": 11, "nodeType": "682", "messageId": "683", "endLine": 26, "endColumn": 14, "suggestions": "739"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 27, "column": 16, "nodeType": "682", "messageId": "683", "endLine": 27, "endColumn": 19, "suggestions": "740"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 70, "column": 24, "nodeType": "682", "messageId": "683", "endLine": 70, "endColumn": 27, "suggestions": "741"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 142, "column": 28, "nodeType": "682", "messageId": "683", "endLine": 142, "endColumn": 31, "suggestions": "742"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 304, "column": 46, "nodeType": "682", "messageId": "683", "endLine": 304, "endColumn": 49, "suggestions": "743"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 304, "column": 56, "nodeType": "682", "messageId": "683", "endLine": 304, "endColumn": 59, "suggestions": "744"}, {"ruleId": "676", "severity": 1, "message": "745", "line": 14, "column": 7, "nodeType": null, "messageId": "678", "endLine": 14, "endColumn": 12}, {"ruleId": "680", "severity": 1, "message": "681", "line": 102, "column": 35, "nodeType": "682", "messageId": "683", "endLine": 102, "endColumn": 38, "suggestions": "746"}, {"ruleId": "676", "severity": 1, "message": "679", "line": 6, "column": 8, "nodeType": null, "messageId": "678", "endLine": 6, "endColumn": 24}, {"ruleId": "676", "severity": 1, "message": "688", "line": 16, "column": 9, "nodeType": null, "messageId": "678", "endLine": 16, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "691", "line": 17, "column": 9, "nodeType": null, "messageId": "678", "endLine": 17, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "747", "line": 324, "column": 9, "nodeType": null, "messageId": "678", "endLine": 324, "endColumn": 28}, {"ruleId": "676", "severity": 1, "message": "748", "line": 84, "column": 9, "nodeType": null, "messageId": "678", "endLine": 84, "endColumn": 18}, {"ruleId": "680", "severity": 1, "message": "681", "line": 61, "column": 55, "nodeType": "682", "messageId": "683", "endLine": 61, "endColumn": 58, "suggestions": "749"}, {"ruleId": "676", "severity": 1, "message": "750", "line": 76, "column": 9, "nodeType": null, "messageId": "678", "endLine": 76, "endColumn": 24}, {"ruleId": "676", "severity": 1, "message": "677", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "676", "severity": 1, "message": "691", "line": 40, "column": 9, "nodeType": null, "messageId": "678", "endLine": 40, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "691", "line": 39, "column": 9, "nodeType": null, "messageId": "678", "endLine": 39, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "691", "line": 37, "column": 9, "nodeType": null, "messageId": "678", "endLine": 37, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "751", "line": 11, "column": 3, "nodeType": null, "messageId": "678", "endLine": 11, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "752", "line": 12, "column": 3, "nodeType": null, "messageId": "678", "endLine": 12, "endColumn": 8}, {"ruleId": "676", "severity": 1, "message": "691", "line": 39, "column": 9, "nodeType": null, "messageId": "678", "endLine": 39, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "753", "line": 8, "column": 3, "nodeType": null, "messageId": "678", "endLine": 8, "endColumn": 8}, {"ruleId": "676", "severity": 1, "message": "754", "line": 12, "column": 3, "nodeType": null, "messageId": "678", "endLine": 12, "endColumn": 14}, {"ruleId": "676", "severity": 1, "message": "691", "line": 40, "column": 9, "nodeType": null, "messageId": "678", "endLine": 40, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "751", "line": 11, "column": 3, "nodeType": null, "messageId": "678", "endLine": 11, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "691", "line": 37, "column": 9, "nodeType": null, "messageId": "678", "endLine": 37, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "691", "line": 38, "column": 9, "nodeType": null, "messageId": "678", "endLine": 38, "endColumn": 16}, {"ruleId": "700", "severity": 1, "message": "701", "line": 333, "column": 53, "nodeType": "702", "messageId": "703", "suggestions": "755"}, {"ruleId": "700", "severity": 1, "message": "701", "line": 333, "column": 71, "nodeType": "702", "messageId": "703", "suggestions": "756"}, {"ruleId": "676", "severity": 1, "message": "757", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "688", "line": 30, "column": 9, "nodeType": null, "messageId": "678", "endLine": 30, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "757", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "758", "line": 17, "column": 3, "nodeType": null, "messageId": "678", "endLine": 17, "endColumn": 7}, {"ruleId": "676", "severity": 1, "message": "688", "line": 29, "column": 9, "nodeType": null, "messageId": "678", "endLine": 29, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "757", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "688", "line": 30, "column": 9, "nodeType": null, "messageId": "678", "endLine": 30, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "757", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "759", "line": 8, "column": 3, "nodeType": null, "messageId": "678", "endLine": 8, "endColumn": 8}, {"ruleId": "676", "severity": 1, "message": "760", "line": 12, "column": 3, "nodeType": null, "messageId": "678", "endLine": 12, "endColumn": 14}, {"ruleId": "676", "severity": 1, "message": "688", "line": 31, "column": 9, "nodeType": null, "messageId": "678", "endLine": 31, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "757", "line": 2, "column": 10, "nodeType": null, "messageId": "678", "endLine": 2, "endColumn": 19}, {"ruleId": "676", "severity": 1, "message": "688", "line": 40, "column": 9, "nodeType": null, "messageId": "678", "endLine": 40, "endColumn": 10}, {"ruleId": "676", "severity": 1, "message": "677", "line": 1, "column": 10, "nodeType": null, "messageId": "678", "endLine": 1, "endColumn": 25}, {"ruleId": "680", "severity": 1, "message": "681", "line": 20, "column": 57, "nodeType": "682", "messageId": "683", "endLine": 20, "endColumn": 60, "suggestions": "761"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 26, "column": 32, "nodeType": "682", "messageId": "683", "endLine": 26, "endColumn": 35, "suggestions": "762"}, {"ruleId": "723", "severity": 1, "message": "763", "line": 22, "column": 9, "nodeType": "725", "endLine": 41, "endColumn": 4}, {"ruleId": "680", "severity": 1, "message": "681", "line": 23, "column": 27, "nodeType": "682", "messageId": "683", "endLine": 23, "endColumn": 30, "suggestions": "764"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 33, "column": 27, "nodeType": "682", "messageId": "683", "endLine": 33, "endColumn": 30, "suggestions": "765"}, {"ruleId": "676", "severity": 1, "message": "766", "line": 10, "column": 44, "nodeType": null, "messageId": "678", "endLine": 10, "endColumn": 50}, {"ruleId": "767", "severity": 1, "message": "768", "line": 32, "column": 7, "nodeType": "769", "endLine": 37, "endColumn": 9}, {"ruleId": "723", "severity": 1, "message": "770", "line": 119, "column": 6, "nodeType": "729", "endLine": 119, "endColumn": 23, "suggestions": "771"}, {"ruleId": "723", "severity": 1, "message": "772", "line": 43, "column": 6, "nodeType": "729", "endLine": 43, "endColumn": 8, "suggestions": "773"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 229, "column": 68, "nodeType": "682", "messageId": "683", "endLine": 229, "endColumn": 71, "suggestions": "774"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 230, "column": 56, "nodeType": "682", "messageId": "683", "endLine": 230, "endColumn": 59, "suggestions": "775"}, {"ruleId": "676", "severity": 1, "message": "776", "line": 4, "column": 36, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 45}, {"ruleId": "676", "severity": 1, "message": "777", "line": 214, "column": 5, "nodeType": null, "messageId": "678", "endLine": 214, "endColumn": 15}, {"ruleId": "676", "severity": 1, "message": "778", "line": 113, "column": 11, "nodeType": null, "messageId": "678", "endLine": 113, "endColumn": 20}, {"ruleId": "676", "severity": 1, "message": "779", "line": 4, "column": 57, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 58}, {"ruleId": "680", "severity": 1, "message": "681", "line": 17, "column": 20, "nodeType": "682", "messageId": "683", "endLine": 17, "endColumn": 23, "suggestions": "780"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 35, "column": 26, "nodeType": "682", "messageId": "683", "endLine": 35, "endColumn": 29, "suggestions": "781"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 43, "column": 55, "nodeType": "682", "messageId": "683", "endLine": 43, "endColumn": 58, "suggestions": "782"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 52, "column": 59, "nodeType": "682", "messageId": "683", "endLine": 52, "endColumn": 62, "suggestions": "783"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 86, "column": 54, "nodeType": "682", "messageId": "683", "endLine": 86, "endColumn": 57, "suggestions": "784"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 117, "column": 56, "nodeType": "682", "messageId": "683", "endLine": 117, "endColumn": 59, "suggestions": "785"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 159, "column": 10, "nodeType": "682", "messageId": "683", "endLine": 159, "endColumn": 13, "suggestions": "786"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 162, "column": 21, "nodeType": "682", "messageId": "683", "endLine": 162, "endColumn": 24, "suggestions": "787"}, {"ruleId": "676", "severity": 1, "message": "788", "line": 4, "column": 21, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 27}, {"ruleId": "676", "severity": 1, "message": "789", "line": 4, "column": 29, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 36}, {"ruleId": "676", "severity": 1, "message": "790", "line": 4, "column": 38, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 46}, {"ruleId": "676", "severity": 1, "message": "791", "line": 4, "column": 55, "nodeType": null, "messageId": "678", "endLine": 4, "endColumn": 63}, {"ruleId": "676", "severity": 1, "message": "792", "line": 5, "column": 10, "nodeType": null, "messageId": "678", "endLine": 5, "endColumn": 21}, {"ruleId": "723", "severity": 1, "message": "793", "line": 62, "column": 27, "nodeType": "794", "endLine": 62, "endColumn": 38}, {"ruleId": "680", "severity": 1, "message": "681", "line": 150, "column": 39, "nodeType": "682", "messageId": "683", "endLine": 150, "endColumn": 42, "suggestions": "795"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 150, "column": 49, "nodeType": "682", "messageId": "683", "endLine": 150, "endColumn": 52, "suggestions": "796"}, {"ruleId": "723", "severity": 1, "message": "797", "line": 63, "column": 6, "nodeType": "729", "endLine": 63, "endColumn": 16, "suggestions": "798"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 7, "column": 34, "nodeType": "682", "messageId": "683", "endLine": 7, "endColumn": 37, "suggestions": "799"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 21, "column": 19, "nodeType": "682", "messageId": "683", "endLine": 21, "endColumn": 22, "suggestions": "800"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 33, "column": 10, "nodeType": "682", "messageId": "683", "endLine": 33, "endColumn": 13, "suggestions": "801"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 58, "column": 40, "nodeType": "682", "messageId": "683", "endLine": 58, "endColumn": 43, "suggestions": "802"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 60, "column": 50, "nodeType": "682", "messageId": "683", "endLine": 60, "endColumn": 53, "suggestions": "803"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 60, "column": 58, "nodeType": "682", "messageId": "683", "endLine": 60, "endColumn": 61, "suggestions": "804"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 76, "column": 57, "nodeType": "682", "messageId": "683", "endLine": 76, "endColumn": 60, "suggestions": "805"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 76, "column": 65, "nodeType": "682", "messageId": "683", "endLine": 76, "endColumn": 68, "suggestions": "806"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 144, "column": 30, "nodeType": "682", "messageId": "683", "endLine": 144, "endColumn": 33, "suggestions": "807"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 158, "column": 28, "nodeType": "682", "messageId": "683", "endLine": 158, "endColumn": 31, "suggestions": "808"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 282, "column": 24, "nodeType": "682", "messageId": "683", "endLine": 282, "endColumn": 27, "suggestions": "809"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 290, "column": 25, "nodeType": "682", "messageId": "683", "endLine": 290, "endColumn": 28, "suggestions": "810"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 292, "column": 12, "nodeType": "682", "messageId": "683", "endLine": 292, "endColumn": 15, "suggestions": "811"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 298, "column": 24, "nodeType": "682", "messageId": "683", "endLine": 298, "endColumn": 27, "suggestions": "812"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 300, "column": 12, "nodeType": "682", "messageId": "683", "endLine": 300, "endColumn": 15, "suggestions": "813"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 306, "column": 27, "nodeType": "682", "messageId": "683", "endLine": 306, "endColumn": 30, "suggestions": "814"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 313, "column": 26, "nodeType": "682", "messageId": "683", "endLine": 313, "endColumn": 29, "suggestions": "815"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 315, "column": 12, "nodeType": "682", "messageId": "683", "endLine": 315, "endColumn": 15, "suggestions": "816"}, {"ruleId": "676", "severity": 1, "message": "817", "line": 104, "column": 9, "nodeType": null, "messageId": "678", "endLine": 104, "endColumn": 20}, {"ruleId": "676", "severity": 1, "message": "818", "line": 114, "column": 9, "nodeType": null, "messageId": "678", "endLine": 114, "endColumn": 16}, {"ruleId": "676", "severity": 1, "message": "817", "line": 163, "column": 9, "nodeType": null, "messageId": "678", "endLine": 163, "endColumn": 20}, {"ruleId": "676", "severity": 1, "message": "818", "line": 174, "column": 9, "nodeType": null, "messageId": "678", "endLine": 174, "endColumn": 16}, {"ruleId": "680", "severity": 1, "message": "681", "line": 35, "column": 45, "nodeType": "682", "messageId": "683", "endLine": 35, "endColumn": 48, "suggestions": "819"}, {"ruleId": "676", "severity": 1, "message": "820", "line": 67, "column": 54, "nodeType": null, "messageId": "678", "endLine": 67, "endColumn": 62}, {"ruleId": "680", "severity": 1, "message": "681", "line": 192, "column": 35, "nodeType": "682", "messageId": "683", "endLine": 192, "endColumn": 38, "suggestions": "821"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 278, "column": 40, "nodeType": "682", "messageId": "683", "endLine": 278, "endColumn": 43, "suggestions": "822"}, {"ruleId": "676", "severity": 1, "message": "823", "line": 95, "column": 16, "nodeType": null, "messageId": "678", "endLine": 95, "endColumn": 17}, {"ruleId": "680", "severity": 1, "message": "681", "line": 165, "column": 27, "nodeType": "682", "messageId": "683", "endLine": 165, "endColumn": 30, "suggestions": "824"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 46, "column": 13, "nodeType": "682", "messageId": "683", "endLine": 46, "endColumn": 16, "suggestions": "825"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 84, "column": 36, "nodeType": "682", "messageId": "683", "endLine": 84, "endColumn": 39, "suggestions": "826"}, {"ruleId": "680", "severity": 1, "message": "681", "line": 15, "column": 55, "nodeType": "682", "messageId": "683", "endLine": 15, "endColumn": 58, "suggestions": "827"}, {"ruleId": "676", "severity": 1, "message": "828", "line": 44, "column": 9, "nodeType": null, "messageId": "678", "endLine": 44, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'getTranslations' is defined but never used.", "unusedVar", "'ImagePlaceholder' is defined but never used.", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["829", "830"], "'IconComponent' is assigned a value but never used.", ["831", "832"], "'Settings' is defined but never used.", "'t' is assigned a value but never used.", "'demoData' is assigned a value but never used.", "'useTranslations' is defined but never used.", "'commonT' is assigned a value but never used.", "'isComplete' is assigned a value but never used.", "'hasNoneSelected' is assigned a value but never used.", ["833", "834"], ["835", "836"], ["837", "838"], ["839", "840"], ["841", "842"], ["843", "844"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["845", "846", "847", "848"], ["849", "850", "851", "852"], ["853", "854"], ["855", "856"], "'error' is defined but never used.", ["857", "858"], ["859", "860"], "'FileText' is defined but never used.", "'LoadingSpinner' is defined but never used.", ["861", "862"], "'isLoading' is assigned a value but never used.", ["863", "864"], ["865", "866"], "'useEffect' is defined but never used.", "'getPainLevelColor' is defined but never used.", "'touched' is assigned a value but never used.", ["867", "868"], "'ConstitutionRecommendations' is defined but never used.", "'constitutionTypeInfo' is defined but never used.", "react-hooks/exhaustive-deps", "The 'questions' conditional could make the dependencies of useCallback Hook (at line 136) change on every render. To fix this, wrap the initialization of 'questions' in its own useMemo() Hook.", "VariableDeclarator", "'err' is defined but never used.", "'NotificationAction' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["869"], "'STORAGE_KEYS' is defined but never used.", ["870", "871"], ["872", "873"], "The 'questions' conditional could make the dependencies of useCallback Hook (at line 457) change on every render. To fix this, wrap the initialization of 'questions' in its own useMemo() Hook.", ["874", "875"], ["876", "877"], ["878", "879"], ["880", "881"], ["882", "883"], ["884", "885"], ["886", "887"], ["888", "889"], ["890", "891"], ["892", "893"], "'inter' is assigned a value but never used.", ["894", "895"], "'conditioningPillars' is assigned a value but never used.", "'homePageT' is assigned a value but never used.", ["896", "897"], "'toggleTreatment' is assigned a value but never used.", "'AlertTriangle' is defined but never used.", "'Clock' is defined but never used.", "'Users' is defined but never used.", "'Thermometer' is defined but never used.", ["898", "899", "900", "901"], ["902", "903", "904", "905"], "'useLocale' is defined but never used.", "'Mail' is defined but never used.", "'Music' is defined but never used.", "'CheckCircle' is defined but never used.", ["906", "907"], ["908", "909"], "The 'phases' array makes the dependencies of useEffect Hook (at line 66) change on every render. To fix this, wrap the initialization of 'phases' in its own useMemo() Hook.", ["910", "911"], ["912", "913"], "'locale' is defined but never used.", "@next/next/no-before-interactive-script-outside-document", "`next/script`'s `beforeInteractive` strategy should not be used outside of `pages/_document.js`. See: https://nextjs.org/docs/messages/no-before-interactive-script-outside-document", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'searchArticles'. Either include it or remove the dependency array.", ["914"], "React Hook useEffect has missing dependencies: 'preferences.accessibility', 'preferences.fontSize', 'preferences.privacy.analytics', 'preferences.theme', and 'recordPageLoadTime'. Either include them or remove the dependency array.", ["915"], ["916", "917"], ["918", "919"], "'BarChart3' is defined but never used.", "'showLabels' is assigned a value but never used.", "'bugReport' is assigned a value but never used.", "'X' is defined but never used.", ["920", "921"], ["922", "923"], ["924", "925"], ["926", "927"], ["928", "929"], ["930", "931"], ["932", "933"], ["934", "935"], "'Filter' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'Bookmark' is defined but never used.", "'useAppStore' is defined but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", ["936", "937"], ["938", "939"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["940"], ["941", "942"], ["943", "944"], ["945", "946"], ["947", "948"], ["949", "950"], ["951", "952"], ["953", "954"], ["955", "956"], ["957", "958"], ["959", "960"], ["961", "962"], ["963", "964"], ["965", "966"], ["967", "968"], ["969", "970"], ["971", "972"], ["973", "974"], ["975", "976"], "'categoryKey' is assigned a value but never used.", "'tagsKey' is assigned a value but never used.", ["977", "978"], "'priority' is assigned a value but never used.", ["979", "980"], ["981", "982"], "'e' is defined but never used.", ["983", "984"], ["985", "986"], ["987", "988"], ["989", "990"], "'fullKey' is assigned a value but never used.", {"messageId": "991", "fix": "992", "desc": "993"}, {"messageId": "994", "fix": "995", "desc": "996"}, {"messageId": "991", "fix": "997", "desc": "993"}, {"messageId": "994", "fix": "998", "desc": "996"}, {"messageId": "991", "fix": "999", "desc": "993"}, {"messageId": "994", "fix": "1000", "desc": "996"}, {"messageId": "991", "fix": "1001", "desc": "993"}, {"messageId": "994", "fix": "1002", "desc": "996"}, {"messageId": "991", "fix": "1003", "desc": "993"}, {"messageId": "994", "fix": "1004", "desc": "996"}, {"messageId": "991", "fix": "1005", "desc": "993"}, {"messageId": "994", "fix": "1006", "desc": "996"}, {"messageId": "991", "fix": "1007", "desc": "993"}, {"messageId": "994", "fix": "1008", "desc": "996"}, {"messageId": "991", "fix": "1009", "desc": "993"}, {"messageId": "994", "fix": "1010", "desc": "996"}, {"messageId": "1011", "data": "1012", "fix": "1013", "desc": "1014"}, {"messageId": "1011", "data": "1015", "fix": "1016", "desc": "1017"}, {"messageId": "1011", "data": "1018", "fix": "1019", "desc": "1020"}, {"messageId": "1011", "data": "1021", "fix": "1022", "desc": "1023"}, {"messageId": "1011", "data": "1024", "fix": "1025", "desc": "1014"}, {"messageId": "1011", "data": "1026", "fix": "1027", "desc": "1017"}, {"messageId": "1011", "data": "1028", "fix": "1029", "desc": "1020"}, {"messageId": "1011", "data": "1030", "fix": "1031", "desc": "1023"}, {"messageId": "991", "fix": "1032", "desc": "993"}, {"messageId": "994", "fix": "1033", "desc": "996"}, {"messageId": "991", "fix": "1034", "desc": "993"}, {"messageId": "994", "fix": "1035", "desc": "996"}, {"messageId": "991", "fix": "1036", "desc": "993"}, {"messageId": "994", "fix": "1037", "desc": "996"}, {"messageId": "991", "fix": "1038", "desc": "993"}, {"messageId": "994", "fix": "1039", "desc": "996"}, {"messageId": "991", "fix": "1040", "desc": "993"}, {"messageId": "994", "fix": "1041", "desc": "996"}, {"messageId": "991", "fix": "1042", "desc": "993"}, {"messageId": "994", "fix": "1043", "desc": "996"}, {"messageId": "991", "fix": "1044", "desc": "993"}, {"messageId": "994", "fix": "1045", "desc": "996"}, {"messageId": "991", "fix": "1046", "desc": "993"}, {"messageId": "994", "fix": "1047", "desc": "996"}, {"desc": "1048", "fix": "1049"}, {"messageId": "991", "fix": "1050", "desc": "993"}, {"messageId": "994", "fix": "1051", "desc": "996"}, {"messageId": "991", "fix": "1052", "desc": "993"}, {"messageId": "994", "fix": "1053", "desc": "996"}, {"messageId": "991", "fix": "1054", "desc": "993"}, {"messageId": "994", "fix": "1055", "desc": "996"}, {"messageId": "991", "fix": "1056", "desc": "993"}, {"messageId": "994", "fix": "1057", "desc": "996"}, {"messageId": "991", "fix": "1058", "desc": "993"}, {"messageId": "994", "fix": "1059", "desc": "996"}, {"messageId": "991", "fix": "1060", "desc": "993"}, {"messageId": "994", "fix": "1061", "desc": "996"}, {"messageId": "991", "fix": "1062", "desc": "993"}, {"messageId": "994", "fix": "1063", "desc": "996"}, {"messageId": "991", "fix": "1064", "desc": "993"}, {"messageId": "994", "fix": "1065", "desc": "996"}, {"messageId": "991", "fix": "1066", "desc": "993"}, {"messageId": "994", "fix": "1067", "desc": "996"}, {"messageId": "991", "fix": "1068", "desc": "993"}, {"messageId": "994", "fix": "1069", "desc": "996"}, {"messageId": "991", "fix": "1070", "desc": "993"}, {"messageId": "994", "fix": "1071", "desc": "996"}, {"messageId": "991", "fix": "1072", "desc": "993"}, {"messageId": "994", "fix": "1073", "desc": "996"}, {"messageId": "991", "fix": "1074", "desc": "993"}, {"messageId": "994", "fix": "1075", "desc": "996"}, {"messageId": "991", "fix": "1076", "desc": "993"}, {"messageId": "994", "fix": "1077", "desc": "996"}, {"messageId": "1011", "data": "1078", "fix": "1079", "desc": "1014"}, {"messageId": "1011", "data": "1080", "fix": "1081", "desc": "1017"}, {"messageId": "1011", "data": "1082", "fix": "1083", "desc": "1020"}, {"messageId": "1011", "data": "1084", "fix": "1085", "desc": "1023"}, {"messageId": "1011", "data": "1086", "fix": "1087", "desc": "1014"}, {"messageId": "1011", "data": "1088", "fix": "1089", "desc": "1017"}, {"messageId": "1011", "data": "1090", "fix": "1091", "desc": "1020"}, {"messageId": "1011", "data": "1092", "fix": "1093", "desc": "1023"}, {"messageId": "991", "fix": "1094", "desc": "993"}, {"messageId": "994", "fix": "1095", "desc": "996"}, {"messageId": "991", "fix": "1096", "desc": "993"}, {"messageId": "994", "fix": "1097", "desc": "996"}, {"messageId": "991", "fix": "1098", "desc": "993"}, {"messageId": "994", "fix": "1099", "desc": "996"}, {"messageId": "991", "fix": "1100", "desc": "993"}, {"messageId": "994", "fix": "1101", "desc": "996"}, {"desc": "1102", "fix": "1103"}, {"desc": "1104", "fix": "1105"}, {"messageId": "991", "fix": "1106", "desc": "993"}, {"messageId": "994", "fix": "1107", "desc": "996"}, {"messageId": "991", "fix": "1108", "desc": "993"}, {"messageId": "994", "fix": "1109", "desc": "996"}, {"messageId": "991", "fix": "1110", "desc": "993"}, {"messageId": "994", "fix": "1111", "desc": "996"}, {"messageId": "991", "fix": "1112", "desc": "993"}, {"messageId": "994", "fix": "1113", "desc": "996"}, {"messageId": "991", "fix": "1114", "desc": "993"}, {"messageId": "994", "fix": "1115", "desc": "996"}, {"messageId": "991", "fix": "1116", "desc": "993"}, {"messageId": "994", "fix": "1117", "desc": "996"}, {"messageId": "991", "fix": "1118", "desc": "993"}, {"messageId": "994", "fix": "1119", "desc": "996"}, {"messageId": "991", "fix": "1120", "desc": "993"}, {"messageId": "994", "fix": "1121", "desc": "996"}, {"messageId": "991", "fix": "1122", "desc": "993"}, {"messageId": "994", "fix": "1123", "desc": "996"}, {"messageId": "991", "fix": "1124", "desc": "993"}, {"messageId": "994", "fix": "1125", "desc": "996"}, {"messageId": "991", "fix": "1126", "desc": "993"}, {"messageId": "994", "fix": "1127", "desc": "996"}, {"messageId": "991", "fix": "1128", "desc": "993"}, {"messageId": "994", "fix": "1129", "desc": "996"}, {"desc": "1130", "fix": "1131"}, {"messageId": "991", "fix": "1132", "desc": "993"}, {"messageId": "994", "fix": "1133", "desc": "996"}, {"messageId": "991", "fix": "1134", "desc": "993"}, {"messageId": "994", "fix": "1135", "desc": "996"}, {"messageId": "991", "fix": "1136", "desc": "993"}, {"messageId": "994", "fix": "1137", "desc": "996"}, {"messageId": "991", "fix": "1138", "desc": "993"}, {"messageId": "994", "fix": "1139", "desc": "996"}, {"messageId": "991", "fix": "1140", "desc": "993"}, {"messageId": "994", "fix": "1141", "desc": "996"}, {"messageId": "991", "fix": "1142", "desc": "993"}, {"messageId": "994", "fix": "1143", "desc": "996"}, {"messageId": "991", "fix": "1144", "desc": "993"}, {"messageId": "994", "fix": "1145", "desc": "996"}, {"messageId": "991", "fix": "1146", "desc": "993"}, {"messageId": "994", "fix": "1147", "desc": "996"}, {"messageId": "991", "fix": "1148", "desc": "993"}, {"messageId": "994", "fix": "1149", "desc": "996"}, {"messageId": "991", "fix": "1150", "desc": "993"}, {"messageId": "994", "fix": "1151", "desc": "996"}, {"messageId": "991", "fix": "1152", "desc": "993"}, {"messageId": "994", "fix": "1153", "desc": "996"}, {"messageId": "991", "fix": "1154", "desc": "993"}, {"messageId": "994", "fix": "1155", "desc": "996"}, {"messageId": "991", "fix": "1156", "desc": "993"}, {"messageId": "994", "fix": "1157", "desc": "996"}, {"messageId": "991", "fix": "1158", "desc": "993"}, {"messageId": "994", "fix": "1159", "desc": "996"}, {"messageId": "991", "fix": "1160", "desc": "993"}, {"messageId": "994", "fix": "1161", "desc": "996"}, {"messageId": "991", "fix": "1162", "desc": "993"}, {"messageId": "994", "fix": "1163", "desc": "996"}, {"messageId": "991", "fix": "1164", "desc": "993"}, {"messageId": "994", "fix": "1165", "desc": "996"}, {"messageId": "991", "fix": "1166", "desc": "993"}, {"messageId": "994", "fix": "1167", "desc": "996"}, {"messageId": "991", "fix": "1168", "desc": "993"}, {"messageId": "994", "fix": "1169", "desc": "996"}, {"messageId": "991", "fix": "1170", "desc": "993"}, {"messageId": "994", "fix": "1171", "desc": "996"}, {"messageId": "991", "fix": "1172", "desc": "993"}, {"messageId": "994", "fix": "1173", "desc": "996"}, {"messageId": "991", "fix": "1174", "desc": "993"}, {"messageId": "994", "fix": "1175", "desc": "996"}, {"messageId": "991", "fix": "1176", "desc": "993"}, {"messageId": "994", "fix": "1177", "desc": "996"}, {"messageId": "991", "fix": "1178", "desc": "993"}, {"messageId": "994", "fix": "1179", "desc": "996"}, {"messageId": "991", "fix": "1180", "desc": "993"}, {"messageId": "994", "fix": "1181", "desc": "996"}, "suggestUnknown", {"range": "1182", "text": "1183"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1184", "text": "1185"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1186", "text": "1183"}, {"range": "1187", "text": "1185"}, {"range": "1188", "text": "1183"}, {"range": "1189", "text": "1185"}, {"range": "1190", "text": "1183"}, {"range": "1191", "text": "1185"}, {"range": "1192", "text": "1183"}, {"range": "1193", "text": "1185"}, {"range": "1194", "text": "1183"}, {"range": "1195", "text": "1185"}, {"range": "1196", "text": "1183"}, {"range": "1197", "text": "1185"}, {"range": "1198", "text": "1183"}, {"range": "1199", "text": "1185"}, "replaceWithAlt", {"alt": "1200"}, {"range": "1201", "text": "1202"}, "Replace with `&quot;`.", {"alt": "1203"}, {"range": "1204", "text": "1205"}, "Replace with `&ldquo;`.", {"alt": "1206"}, {"range": "1207", "text": "1208"}, "Replace with `&#34;`.", {"alt": "1209"}, {"range": "1210", "text": "1211"}, "Replace with `&rdquo;`.", {"alt": "1200"}, {"range": "1212", "text": "1213"}, {"alt": "1203"}, {"range": "1214", "text": "1215"}, {"alt": "1206"}, {"range": "1216", "text": "1217"}, {"alt": "1209"}, {"range": "1218", "text": "1219"}, {"range": "1220", "text": "1183"}, {"range": "1221", "text": "1185"}, {"range": "1222", "text": "1183"}, {"range": "1223", "text": "1185"}, {"range": "1224", "text": "1183"}, {"range": "1225", "text": "1185"}, {"range": "1226", "text": "1183"}, {"range": "1227", "text": "1185"}, {"range": "1228", "text": "1183"}, {"range": "1229", "text": "1185"}, {"range": "1230", "text": "1183"}, {"range": "1231", "text": "1185"}, {"range": "1232", "text": "1183"}, {"range": "1233", "text": "1185"}, {"range": "1234", "text": "1183"}, {"range": "1235", "text": "1185"}, "Update the dependencies array to be: [removeNotification]", {"range": "1236", "text": "1237"}, {"range": "1238", "text": "1183"}, {"range": "1239", "text": "1185"}, {"range": "1240", "text": "1183"}, {"range": "1241", "text": "1185"}, {"range": "1242", "text": "1183"}, {"range": "1243", "text": "1185"}, {"range": "1244", "text": "1183"}, {"range": "1245", "text": "1185"}, {"range": "1246", "text": "1183"}, {"range": "1247", "text": "1185"}, {"range": "1248", "text": "1183"}, {"range": "1249", "text": "1185"}, {"range": "1250", "text": "1183"}, {"range": "1251", "text": "1185"}, {"range": "1252", "text": "1183"}, {"range": "1253", "text": "1185"}, {"range": "1254", "text": "1183"}, {"range": "1255", "text": "1185"}, {"range": "1256", "text": "1183"}, {"range": "1257", "text": "1185"}, {"range": "1258", "text": "1183"}, {"range": "1259", "text": "1185"}, {"range": "1260", "text": "1183"}, {"range": "1261", "text": "1185"}, {"range": "1262", "text": "1183"}, {"range": "1263", "text": "1185"}, {"range": "1264", "text": "1183"}, {"range": "1265", "text": "1185"}, {"alt": "1200"}, {"range": "1266", "text": "1200"}, {"alt": "1203"}, {"range": "1267", "text": "1203"}, {"alt": "1206"}, {"range": "1268", "text": "1206"}, {"alt": "1209"}, {"range": "1269", "text": "1209"}, {"alt": "1200"}, {"range": "1270", "text": "1200"}, {"alt": "1203"}, {"range": "1271", "text": "1203"}, {"alt": "1206"}, {"range": "1272", "text": "1206"}, {"alt": "1209"}, {"range": "1273", "text": "1209"}, {"range": "1274", "text": "1183"}, {"range": "1275", "text": "1185"}, {"range": "1276", "text": "1183"}, {"range": "1277", "text": "1185"}, {"range": "1278", "text": "1183"}, {"range": "1279", "text": "1185"}, {"range": "1280", "text": "1183"}, {"range": "1281", "text": "1185"}, "Update the dependencies array to be: [query, articles, searchArticles]", {"range": "1282", "text": "1283"}, "Update the dependencies array to be: [preferences.accessibility, preferences.fontSize, preferences.privacy.analytics, preferences.theme, recordPageLoadTime]", {"range": "1284", "text": "1285"}, {"range": "1286", "text": "1183"}, {"range": "1287", "text": "1185"}, {"range": "1288", "text": "1183"}, {"range": "1289", "text": "1185"}, {"range": "1290", "text": "1183"}, {"range": "1291", "text": "1185"}, {"range": "1292", "text": "1183"}, {"range": "1293", "text": "1185"}, {"range": "1294", "text": "1183"}, {"range": "1295", "text": "1185"}, {"range": "1296", "text": "1183"}, {"range": "1297", "text": "1185"}, {"range": "1298", "text": "1183"}, {"range": "1299", "text": "1185"}, {"range": "1300", "text": "1183"}, {"range": "1301", "text": "1185"}, {"range": "1302", "text": "1183"}, {"range": "1303", "text": "1185"}, {"range": "1304", "text": "1183"}, {"range": "1305", "text": "1185"}, {"range": "1306", "text": "1183"}, {"range": "1307", "text": "1185"}, {"range": "1308", "text": "1183"}, {"range": "1309", "text": "1185"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "1310", "text": "1311"}, {"range": "1312", "text": "1183"}, {"range": "1313", "text": "1185"}, {"range": "1314", "text": "1183"}, {"range": "1315", "text": "1185"}, {"range": "1316", "text": "1183"}, {"range": "1317", "text": "1185"}, {"range": "1318", "text": "1183"}, {"range": "1319", "text": "1185"}, {"range": "1320", "text": "1183"}, {"range": "1321", "text": "1185"}, {"range": "1322", "text": "1183"}, {"range": "1323", "text": "1185"}, {"range": "1324", "text": "1183"}, {"range": "1325", "text": "1185"}, {"range": "1326", "text": "1183"}, {"range": "1327", "text": "1185"}, {"range": "1328", "text": "1183"}, {"range": "1329", "text": "1185"}, {"range": "1330", "text": "1183"}, {"range": "1331", "text": "1185"}, {"range": "1332", "text": "1183"}, {"range": "1333", "text": "1185"}, {"range": "1334", "text": "1183"}, {"range": "1335", "text": "1185"}, {"range": "1336", "text": "1183"}, {"range": "1337", "text": "1185"}, {"range": "1338", "text": "1183"}, {"range": "1339", "text": "1185"}, {"range": "1340", "text": "1183"}, {"range": "1341", "text": "1185"}, {"range": "1342", "text": "1183"}, {"range": "1343", "text": "1185"}, {"range": "1344", "text": "1183"}, {"range": "1345", "text": "1185"}, {"range": "1346", "text": "1183"}, {"range": "1347", "text": "1185"}, {"range": "1348", "text": "1183"}, {"range": "1349", "text": "1185"}, {"range": "1350", "text": "1183"}, {"range": "1351", "text": "1185"}, {"range": "1352", "text": "1183"}, {"range": "1353", "text": "1185"}, {"range": "1354", "text": "1183"}, {"range": "1355", "text": "1185"}, {"range": "1356", "text": "1183"}, {"range": "1357", "text": "1185"}, {"range": "1358", "text": "1183"}, {"range": "1359", "text": "1185"}, {"range": "1360", "text": "1183"}, {"range": "1361", "text": "1185"}, [7624, 7627], "unknown", [7624, 7627], "never", [1121, 1124], [1121, 1124], [4779, 4782], [4779, 4782], [4840, 4843], [4840, 4843], [5046, 5049], [5046, 5049], [5111, 5114], [5111, 5114], [20643, 20646], [20643, 20646], [21447, 21450], [21447, 21450], "&quot;", [28512, 28540], "\n                          &quot;", "&ldquo;", [28512, 28540], "\n                          &ldquo;", "&#34;", [28512, 28540], "\n                          &#34;", "&rdquo;", [28512, 28540], "\n                          &rdquo;", [28558, 28584], "&quot;\n                        ", [28558, 28584], "&ldquo;\n                        ", [28558, 28584], "&#34;\n                        ", [28558, 28584], "&rdquo;\n                        ", [707, 710], [707, 710], [1369, 1372], [1369, 1372], [1967, 1970], [1967, 1970], [3289, 3292], [3289, 3292], [947, 950], [947, 950], [1691, 1694], [1691, 1694], [18731, 18734], [18731, 18734], [2503, 2506], [2503, 2506], [1474, 1476], "[removeNotification]", [3367, 3370], [3367, 3370], [846, 849], [846, 849], [5165, 5168], [5165, 5168], [14352, 14355], [14352, 14355], [442, 445], [442, 445], [485, 488], [485, 488], [500, 503], [500, 503], [520, 523], [520, 523], [1470, 1473], [1470, 1473], [3191, 3194], [3191, 3194], [8489, 8492], [8489, 8492], [8499, 8502], [8499, 8502], [2444, 2447], [2444, 2447], [1522, 1525], [1522, 1525], [14914, 14915], [14914, 14915], [14914, 14915], [14914, 14915], [14932, 14933], [14932, 14933], [14932, 14933], [14932, 14933], [572, 575], [572, 575], [678, 681], [678, 681], [394, 397], [394, 397], [574, 577], [574, 577], [3277, 3294], "[query, articles, searchArticles]", [1113, 1115], "[preferences.accessibility, preferences.fontSize, preferences.privacy.analytics, preferences.theme, recordPageLoadTime]", [5393, 5396], [5393, 5396], [5458, 5461], [5458, 5461], [490, 493], [490, 493], [852, 855], [852, 855], [1040, 1043], [1040, 1043], [1275, 1278], [1275, 1278], [2195, 2198], [2195, 2198], [3043, 3046], [3043, 3046], [3910, 3913], [3910, 3913], [3974, 3977], [3974, 3977], [3826, 3829], [3826, 3829], [3836, 3839], [3836, 3839], [1352, 1362], "[duration, handleClose]", [171, 174], [171, 174], [432, 435], [432, 435], [699, 702], [699, 702], [1166, 1169], [1166, 1169], [1319, 1322], [1319, 1322], [1327, 1330], [1327, 1330], [1746, 1749], [1746, 1749], [1754, 1757], [1754, 1757], [3285, 3288], [3285, 3288], [3604, 3607], [3604, 3607], [6873, 6876], [6873, 6876], [7106, 7109], [7106, 7109], [7140, 7143], [7140, 7143], [7332, 7335], [7332, 7335], [7366, 7369], [7366, 7369], [7560, 7563], [7560, 7563], [7753, 7756], [7753, 7756], [7787, 7790], [7787, 7790], [658, 661], [658, 661], [4084, 4087], [4084, 4087], [5935, 5938], [5935, 5938], [4331, 4334], [4331, 4334], [917, 920], [917, 920], [1686, 1689], [1686, 1689], [317, 320], [317, 320]]