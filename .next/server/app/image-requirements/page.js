(()=>{var e={};e.id=5298,e.ids=[5298],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},13962:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>r.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>g,tree:()=>c}),a(22955),a(21149),a(35866);var i=a(23191),s=a(88716),n=a(37922),r=a.n(n),l=a(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c=["",{children:["image-requirements",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,22955)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx"],h="/image-requirements/page",m={require:a,loadChunk:()=>Promise.resolve()},g=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/image-requirements/page",pathname:"/image-requirements",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},11386:(e,t,a)=>{Promise.resolve().then(a.bind(a,84348))},84348:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n});var i=a(10326);function s({filename:e,alt:t,width:a=400,height:s=300,className:n="",description:r}){return(0,i.jsxs)("div",{className:`bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 ${n}`,style:{width:a,height:s,minHeight:s},children:[i.jsx("div",{className:"text-gray-400 text-2xl mb-2",children:"\uD83D\uDCF7"}),(0,i.jsxs)("div",{className:"text-center",children:[i.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e}),(0,i.jsxs)("p",{className:"text-xs text-gray-500 mb-2",children:[a,"x",s,"px"]}),r&&i.jsx("p",{className:"text-xs text-gray-400 max-w-xs",children:r}),(0,i.jsxs)("p",{className:"text-xs text-gray-400 mt-2 italic",children:["Alt: ",t]})]})]})}function n(){return i.jsx("div",{className:"min-h-screen bg-gray-50 py-8",children:(0,i.jsxs)("div",{className:"container mx-auto px-4",children:[(0,i.jsxs)("header",{className:"text-center mb-12",children:[i.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83C\uDFA8 PeriodHub Health 图片需求清单"}),i.jsx("p",{className:"text-lg text-gray-600 max-w-3xl mx-auto",children:"以下是网站所需的所有图片资源，包含详细的规格要求、提示词和存放路径。 每个占位符展示了图片的具体要求和设计指导。"}),i.jsx("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:(0,i.jsxs)("p",{className:"text-sm text-blue-800",children:[i.jsx("strong",{children:"总计需求:"})," 约80-100张图片 |",i.jsx("strong",{children:"预估制作时间:"})," 2-4周 |",i.jsx("strong",{children:"优先级:"})," 高优先级图片已标注"]})})]}),i.jsx("div",{className:"space-y-12",children:[{category:"Hero Section",images:[{filename:"hero-main-banner.jpg",alt:"Professional healthcare illustration showing diverse women in comfortable poses",width:800,height:450,path:"/public/images/hero/",description:"Warm and professional healthcare illustration, young diverse women in comfortable poses, soft pink and blue gradient background, modern minimalist style, conveying comfort and medical trust"}]},{category:"Feature Icons",images:[{filename:"feature-assessment.svg",alt:"Symptom assessment icon",width:64,height:64,path:"/public/images/icons/features/",description:"Medical assessment icon, clean line art style, pink and blue color scheme, professional healthcare symbol"},{filename:"feature-tracker.svg",alt:"Pain tracking icon",width:64,height:64,path:"/public/images/icons/features/",description:"Pain tracking icon, modern flat design, warm colors, user-friendly medical symbol"},{filename:"feature-visualization.svg",alt:"Data visualization icon",width:64,height:64,path:"/public/images/icons/features/",description:"Data visualization icon, clean modern design, pink and blue gradients, professional healthcare analytics"}]},{category:"Interactive Tools",images:[{filename:"assessment-illustration.jpg",alt:"Woman using digital health assessment tool on tablet",width:600,height:400,path:"/public/images/tools/",description:"Woman using digital health assessment tool, modern tablet interface, comfortable home setting, soft lighting, professional medical app design"},{filename:"pain-tracker-dashboard.jpg",alt:"Digital health tracking dashboard interface",width:800,height:500,path:"/public/images/tools/",description:"Digital health tracking dashboard, clean UI design, data charts and calendars, pink and blue color scheme, modern healthcare technology"}]},{category:"Article Category Covers",images:[{filename:"category-medical-guide-cover.jpg",alt:"Medical textbook and stethoscope on clean desk",width:800,height:450,path:"/public/images/articles/categories/",description:"Medical textbook and stethoscope on clean desk, professional healthcare setting, soft natural lighting, pink and blue accents"},{filename:"category-natural-therapy-cover.jpg",alt:"Natural healing elements in spa-like setting",width:800,height:450,path:"/public/images/articles/categories/",description:"Natural healing elements, herbal tea, essential oils, yoga mat, serene spa-like setting, warm natural lighting"},{filename:"category-lifestyle-cover.jpg",alt:"Healthy lifestyle elements in modern kitchen",width:800,height:450,path:"/public/images/articles/categories/",description:"Healthy lifestyle elements, fresh fruits, exercise equipment, journal, bright modern kitchen setting"}]},{category:"Scenario Solutions",images:[{filename:"scenario-office.jpg",alt:"Professional woman managing menstrual discomfort at office",width:600,height:400,path:"/public/images/scenarios/",description:"Professional woman managing menstrual discomfort at office, discrete pain relief methods, modern workplace setting"},{filename:"scenario-exercise.jpg",alt:"Woman adapting exercise routine during menstruation",width:600,height:400,path:"/public/images/scenarios/",description:"Woman adapting exercise routine during menstruation, gym setting, showing modified workout techniques"},{filename:"scenario-social.jpg",alt:"Woman confidently participating in social activities",width:600,height:400,path:"/public/images/scenarios/",description:"Woman confidently participating in social activities, restaurant or cafe setting, showing comfort and confidence"}]},{category:"Treatment Methods",images:[{filename:"treatment-heat-therapy.jpg",alt:"Woman applying heating pad to lower abdomen",width:600,height:400,path:"/public/images/treatments/",description:"Woman applying heating pad to lower abdomen, comfortable home setting, soft warm lighting, relaxed expression"},{filename:"treatment-yoga-poses.jpg",alt:"Woman in yoga pose for menstrual pain relief",width:600,height:400,path:"/public/images/treatments/",description:"Woman in yoga pose for menstrual pain relief, peaceful studio setting, natural lighting, demonstrating proper form"}]},{category:"Traditional Chinese Medicine",images:[{filename:"tcm-acupuncture.jpg",alt:"Professional acupuncture treatment session",width:600,height:400,path:"/public/images/tcm/",description:"Professional acupuncture treatment, clean clinical setting, traditional Chinese medicine elements, professional and trustworthy"},{filename:"tcm-herbal-medicine.jpg",alt:"Traditional Chinese herbs and medicine preparation",width:600,height:400,path:"/public/images/tcm/",description:"Traditional Chinese herbs and medicine preparation, wooden bowls, natural ingredients, warm traditional setting"}]},{category:"Data & Charts",images:[{filename:"stats-infographic.svg",alt:"Medical statistics infographic with clean data visualization",width:800,height:400,path:"/public/images/infographics/",description:"Medical statistics infographic, clean data visualization, pink and blue color scheme, professional charts and graphs showing women's health data"},{filename:"chart-pain-statistics.svg",alt:"Menstrual pain prevalence statistics chart",width:600,height:400,path:"/public/images/charts/",description:"Medical statistics chart, menstrual pain prevalence data, clean infographic style, pink and blue color scheme"}]}].map((e,t)=>(0,i.jsxs)("section",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,i.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-6 border-b border-gray-200 pb-3",children:["\uD83D\uDCC1 ",e.category]}),i.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.images.map((e,t)=>(0,i.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[i.jsx(s,{filename:e.filename,alt:e.alt,width:e.width,height:e.height,className:"w-full mb-4",description:e.description}),(0,i.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"font-medium text-gray-700",children:"路径:"}),i.jsx("span",{className:"text-gray-600 font-mono text-xs",children:e.path})]}),(0,i.jsxs)("div",{className:"flex justify-between",children:[i.jsx("span",{className:"font-medium text-gray-700",children:"尺寸:"}),(0,i.jsxs)("span",{className:"text-gray-600",children:[e.width,"x",e.height,"px"]})]}),(0,i.jsxs)("div",{className:"mt-3",children:[i.jsx("span",{className:"font-medium text-gray-700",children:"提示词:"}),i.jsx("p",{className:"text-gray-600 text-xs mt-1 leading-relaxed",children:e.description})]})]})]},t))})]},t))}),i.jsx("footer",{className:"mt-16 text-center",children:(0,i.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-8",children:[i.jsx("h3",{className:"text-xl font-bold text-gray-800 mb-4",children:"\uD83D\uDCCB 下一步行动"}),(0,i.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 text-sm",children:[(0,i.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[i.jsx("h4",{className:"font-semibold text-pink-600 mb-2",children:"高优先级 (立即需要)"}),(0,i.jsxs)("ul",{className:"text-gray-600 space-y-1",children:[i.jsx("li",{children:"• Hero主视觉图片"}),i.jsx("li",{children:"• 核心功能图标"}),i.jsx("li",{children:"• 文章分类封面"}),i.jsx("li",{children:"• 基础治疗方法图解"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[i.jsx("h4",{className:"font-semibold text-blue-600 mb-2",children:"中优先级 (1-2周内)"}),(0,i.jsxs)("ul",{className:"text-gray-600 space-y-1",children:[i.jsx("li",{children:"• 情景解决方案图片"}),i.jsx("li",{children:"• 中医疗法图解"}),i.jsx("li",{children:"• 解剖结构图"}),i.jsx("li",{children:"• 生活方式图片"})]})]}),(0,i.jsxs)("div",{className:"bg-white rounded-lg p-4",children:[i.jsx("h4",{className:"font-semibold text-purple-600 mb-2",children:"低优先级 (1个月内)"}),(0,i.jsxs)("ul",{className:"text-gray-600 space-y-1",children:[i.jsx("li",{children:"• 装饰性图片"}),i.jsx("li",{children:"• 背景纹理"}),i.jsx("li",{children:"• 移动端优化图片"}),i.jsx("li",{children:"• 高级数据可视化"})]})]})]})]})})]})})}a(17577)},22955:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});let i=(0,a(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx#default`)},87421:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>n}),a(19510);var i=a(66621);let s={runtime:"edge",size:{width:32,height:32},contentType:"image/png"};async function n(e){let{__metadata_id__:t,...a}=e.params,n=(0,i.fillMetadataSegment)(".",a,"icon"),{generateImageMetadata:r}=s;function l(e,t){let a={alt:e.alt,type:e.contentType||"image/png",url:n+(t?"/"+t:"")+"?040dbbaeb70c8654"},{size:i}=e;return i&&(a.sizes=i.width+"x"+i.height),a}return r?(await r({params:a})).map((e,t)=>{let a=(e.id||t)+"";return l(e,a)}):[l(s,"")]}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[8948,1386,6621,2810],()=>a(13962));module.exports=i})();