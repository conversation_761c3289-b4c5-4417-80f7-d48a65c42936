(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[826],{6002:(e,t,r)=>{var i={"./en.json":3981,"./zh.json":4874};function n(e){return Promise.resolve().then(()=>{if(!r.o(i,e)){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}var n=i[e];return r.t(n,19)})}n.keys=()=>Object.keys(i),n.id=6002,e.exports=n},2067:e=>{"use strict";e.exports=require("node:async_hooks")},6195:e=>{"use strict";e.exports=require("node:buffer")},4468:(e,t,r)=>{"use strict";let i;r.r(t),r.d(t,{default:()=>eC});var n,a,s,o,l,d,c,u,p,m,g,h,f={};async function y(){let e="_ENTRIES"in globalThis&&_ENTRIES.middleware_instrumentation&&(await _ENTRIES.middleware_instrumentation).register;if(e)try{await e()}catch(e){throw e.message=`An error occurred while loading instrumentation hook: ${e.message}`,e}}r.r(f),r.d(f,{config:()=>eS,default:()=>eb});let _=null;function v(){return _||(_=y()),_}function w(e){return`The edge runtime does not support Node.js '${e}' module.
Learn More: https://nextjs.org/docs/messages/node-module-in-edge-runtime`}process!==r.g.process&&(process.env=r.g.process.env,r.g.process=process),Object.defineProperty(globalThis,"__import_unsupported",{value:function(e){let t=new Proxy(function(){},{get(t,r){if("then"===r)return{};throw Error(w(e))},construct(){throw Error(w(e))},apply(r,i,n){if("function"==typeof n[0])return n[0](t);throw Error(w(e))}});return new Proxy({},{get:()=>t})},enumerable:!1,configurable:!1}),v();var b=r(6416),S=r(6329);let T=Symbol("response"),x=Symbol("passThrough"),P=Symbol("waitUntil");class C{constructor(e){this[P]=[],this[x]=!1}respondWith(e){this[T]||(this[T]=Promise.resolve(e))}passThroughOnException(){this[x]=!0}waitUntil(e){this[P].push(e)}}class M extends C{constructor(e){super(e.request),this.sourcePage=e.page}get request(){throw new b.qJ({page:this.sourcePage})}respondWith(){throw new b.qJ({page:this.sourcePage})}}var A=r(1669),R=r(8241);function E(e,t){let r="string"==typeof t?new URL(t):t,i=new URL(e,t),n=r.protocol+"//"+r.host;return i.protocol+"//"+i.host===n?i.toString().replace(n,""):i.toString()}var L=r(9718);let N=[["RSC"],["Next-Router-State-Tree"],["Next-Router-Prefetch"]],O=["__nextFallback","__nextLocale","__nextInferredLocaleFromDefault","__nextDefaultLocale","__nextIsNotFound","_rsc"],k=["__nextDataReq"];var D=r(7217);class I extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new I}}class q extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,i){if("symbol"==typeof r)return D.g.get(t,r,i);let n=r.toLowerCase(),a=Object.keys(e).find(e=>e.toLowerCase()===n);if(void 0!==a)return D.g.get(t,a,i)},set(t,r,i,n){if("symbol"==typeof r)return D.g.set(t,r,i,n);let a=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===a);return D.g.set(t,s??r,i,n)},has(t,r){if("symbol"==typeof r)return D.g.has(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0!==n&&D.g.has(t,n)},deleteProperty(t,r){if("symbol"==typeof r)return D.g.deleteProperty(t,r);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);return void 0===n||D.g.deleteProperty(t,n)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return I.callable;default:return D.g.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new q(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,i]of this.entries())e.call(t,i,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}var B=r(938),G=r(5558);class j extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#cookiessetname-value-options")}static callable(){throw new j}}class U{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return j.callable;default:return D.g.get(e,t,r)}}})}}let H=Symbol.for("next.mutated.cookies");class F{static wrap(e,t){let r=new B.nV(new Headers);for(let t of e.getAll())r.set(t);let i=[],n=new Set,a=()=>{let e=G.A.getStore();if(e&&(e.pathWasRevalidated=!0),i=r.getAll().filter(e=>n.has(e.name)),t){let e=[];for(let t of i){let r=new B.nV(new Headers);r.set(t),e.push(r.toString())}t(e)}};return new Proxy(r,{get(e,t,r){switch(t){case H:return i;case"delete":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{e.delete(...t)}finally{a()}};case"set":return function(...t){n.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t)}finally{a()}};default:return D.g.get(e,t,r)}}})}}var V=r(300);!function(e){e.handleRequest="BaseServer.handleRequest",e.run="BaseServer.run",e.pipe="BaseServer.pipe",e.getStaticHTML="BaseServer.getStaticHTML",e.render="BaseServer.render",e.renderToResponseWithComponents="BaseServer.renderToResponseWithComponents",e.renderToResponse="BaseServer.renderToResponse",e.renderToHTML="BaseServer.renderToHTML",e.renderError="BaseServer.renderError",e.renderErrorToResponse="BaseServer.renderErrorToResponse",e.renderErrorToHTML="BaseServer.renderErrorToHTML",e.render404="BaseServer.render404"}(n||(n={})),function(e){e.loadDefaultErrorComponents="LoadComponents.loadDefaultErrorComponents",e.loadComponents="LoadComponents.loadComponents"}(a||(a={})),function(e){e.getRequestHandler="NextServer.getRequestHandler",e.getServer="NextServer.getServer",e.getServerRequestHandler="NextServer.getServerRequestHandler",e.createServer="createServer.createServer"}(s||(s={})),function(e){e.compression="NextNodeServer.compression",e.getBuildId="NextNodeServer.getBuildId",e.createComponentTree="NextNodeServer.createComponentTree",e.clientComponentLoading="NextNodeServer.clientComponentLoading",e.getLayoutOrPageModule="NextNodeServer.getLayoutOrPageModule",e.generateStaticRoutes="NextNodeServer.generateStaticRoutes",e.generateFsStaticRoutes="NextNodeServer.generateFsStaticRoutes",e.generatePublicRoutes="NextNodeServer.generatePublicRoutes",e.generateImageRoutes="NextNodeServer.generateImageRoutes.route",e.sendRenderResult="NextNodeServer.sendRenderResult",e.proxyRequest="NextNodeServer.proxyRequest",e.runApi="NextNodeServer.runApi",e.render="NextNodeServer.render",e.renderHTML="NextNodeServer.renderHTML",e.imageOptimizer="NextNodeServer.imageOptimizer",e.getPagePath="NextNodeServer.getPagePath",e.getRoutesManifest="NextNodeServer.getRoutesManifest",e.findPageComponents="NextNodeServer.findPageComponents",e.getFontManifest="NextNodeServer.getFontManifest",e.getServerComponentManifest="NextNodeServer.getServerComponentManifest",e.getRequestHandler="NextNodeServer.getRequestHandler",e.renderToHTML="NextNodeServer.renderToHTML",e.renderError="NextNodeServer.renderError",e.renderErrorToHTML="NextNodeServer.renderErrorToHTML",e.render404="NextNodeServer.render404",e.startResponse="NextNodeServer.startResponse",e.route="route",e.onProxyReq="onProxyReq",e.apiResolver="apiResolver",e.internalFetch="internalFetch"}(o||(o={})),(l||(l={})).startServer="startServer.startServer",function(e){e.getServerSideProps="Render.getServerSideProps",e.getStaticProps="Render.getStaticProps",e.renderToString="Render.renderToString",e.renderDocument="Render.renderDocument",e.createBodyResult="Render.createBodyResult"}(d||(d={})),function(e){e.renderToString="AppRender.renderToString",e.renderToReadableStream="AppRender.renderToReadableStream",e.getBodyResult="AppRender.getBodyResult",e.fetch="AppRender.fetch"}(c||(c={})),(u||(u={})).executeRoute="Router.executeRoute",(p||(p={})).runHandler="Node.runHandler",(m||(m={})).runHandler="AppRouteRouteHandlers.runHandler",function(e){e.generateMetadata="ResolveMetadata.generateMetadata",e.generateViewport="ResolveMetadata.generateViewport"}(g||(g={})),(h||(h={})).execute="Middleware.execute";let z=["Middleware.execute","BaseServer.handleRequest","Render.getServerSideProps","Render.getStaticProps","AppRender.fetch","AppRender.getBodyResult","Render.renderDocument","Node.runHandler","AppRouteRouteHandlers.runHandler","ResolveMetadata.generateMetadata","ResolveMetadata.generateViewport","NextNodeServer.createComponentTree","NextNodeServer.findPageComponents","NextNodeServer.getLayoutOrPageModule","NextNodeServer.startResponse","NextNodeServer.clientComponentLoading"],W=["NextNodeServer.findPageComponents","NextNodeServer.createComponentTree","NextNodeServer.clientComponentLoading"],{context:K,propagation:Y,trace:$,SpanStatusCode:Z,SpanKind:J,ROOT_CONTEXT:Q}=i=r(8439),X=e=>null!==e&&"object"==typeof e&&"function"==typeof e.then,ee=(e,t)=>{(null==t?void 0:t.bubble)===!0?e.setAttribute("next.bubble",!0):(t&&e.recordException(t),e.setStatus({code:Z.ERROR,message:null==t?void 0:t.message})),e.end()},et=new Map,er=i.createContextKey("next.rootSpanId"),ei=0,en=()=>ei++;class ea{getTracerInstance(){return $.getTracer("next.js","0.0.1")}getContext(){return K}getActiveScopeSpan(){return $.getSpan(null==K?void 0:K.active())}withPropagatedContext(e,t,r){let i=K.active();if($.getSpanContext(i))return t();let n=Y.extract(i,e,r);return K.with(n,t)}trace(...e){var t;let[r,i,n]=e,{fn:a,options:s}="function"==typeof i?{fn:i,options:{}}:{fn:n,options:{...i}},o=s.spanName??r;if(!z.includes(r)&&"1"!==process.env.NEXT_OTEL_VERBOSE||s.hideSpan)return a();let l=this.getSpanContext((null==s?void 0:s.parentSpan)??this.getActiveScopeSpan()),d=!1;l?(null==(t=$.getSpanContext(l))?void 0:t.isRemote)&&(d=!0):(l=(null==K?void 0:K.active())??Q,d=!0);let c=en();return s.attributes={"next.span_name":o,"next.span_type":r,...s.attributes},K.with(l.setValue(er,c),()=>this.getTracerInstance().startActiveSpan(o,s,e=>{let t="performance"in globalThis?globalThis.performance.now():void 0,i=()=>{et.delete(c),t&&process.env.NEXT_OTEL_PERFORMANCE_PREFIX&&W.includes(r||"")&&performance.measure(`${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-${(r.split(".").pop()||"").replace(/[A-Z]/g,e=>"-"+e.toLowerCase())}`,{start:t,end:performance.now()})};d&&et.set(c,new Map(Object.entries(s.attributes??{})));try{if(a.length>1)return a(e,t=>ee(e,t));let t=a(e);if(X(t))return t.then(t=>(e.end(),t)).catch(t=>{throw ee(e,t),t}).finally(i);return e.end(),i(),t}catch(t){throw ee(e,t),i(),t}}))}wrap(...e){let t=this,[r,i,n]=3===e.length?e:[e[0],{},e[1]];return z.includes(r)||"1"===process.env.NEXT_OTEL_VERBOSE?function(){let e=i;"function"==typeof e&&"function"==typeof n&&(e=e.apply(this,arguments));let a=arguments.length-1,s=arguments[a];if("function"!=typeof s)return t.trace(r,e,()=>n.apply(this,arguments));{let i=t.getContext().bind(K.active(),s);return t.trace(r,e,(e,t)=>(arguments[a]=function(e){return null==t||t(e),i.apply(this,arguments)},n.apply(this,arguments)))}}:n}startSpan(...e){let[t,r]=e,i=this.getSpanContext((null==r?void 0:r.parentSpan)??this.getActiveScopeSpan());return this.getTracerInstance().startSpan(t,r,i)}getSpanContext(e){return e?$.setSpan(K.active(),e):void 0}getRootSpanAttributes(){let e=K.active().getValue(er);return et.get(e)}}let es=(()=>{let e=new ea;return()=>e})(),eo="__prerender_bypass";Symbol("__next_preview_data"),Symbol(eo);class el{constructor(e,t,r,i){var n;let a=e&&function(e,t){let r=q.from(e.headers);return{isOnDemandRevalidate:r.get(V.y3)===t.previewModeId,revalidateOnlyGenerated:r.has(V.Qq)}}(t,e).isOnDemandRevalidate,s=null==(n=r.get(eo))?void 0:n.value;this.isEnabled=!!(!a&&s&&e&&s===e.previewModeId),this._previewModeId=null==e?void 0:e.previewModeId,this._mutableCookies=i}enable(){if(!this._previewModeId)throw Error("Invariant: previewProps missing previewModeId this should never happen");this._mutableCookies.set({name:eo,value:this._previewModeId,httpOnly:!0,sameSite:"none",secure:!0,path:"/"})}disable(){this._mutableCookies.set({name:eo,value:"",httpOnly:!0,sameSite:"none",secure:!0,path:"/",expires:new Date(0)})}}function ed(e,t){if("x-middleware-set-cookie"in e.headers&&"string"==typeof e.headers["x-middleware-set-cookie"]){let r=e.headers["x-middleware-set-cookie"],i=new Headers;for(let e of(0,S.l$)(r))i.append("set-cookie",e);for(let e of new B.nV(i).getAll())t.set(e)}}let ec={wrap(e,{req:t,res:r,renderOpts:i},n){let a;function s(e){r&&r.setHeader("Set-Cookie",e)}i&&"previewProps"in i&&(a=i.previewProps);let o={},l={get headers(){return o.headers||(o.headers=function(e){let t=q.from(e);for(let e of N)t.delete(e.toString().toLowerCase());return q.seal(t)}(t.headers)),o.headers},get cookies(){if(!o.cookies){let e=new B.qC(q.from(t.headers));ed(t,e),o.cookies=U.seal(e)}return o.cookies},get mutableCookies(){if(!o.mutableCookies){let e=function(e,t){let r=new B.qC(q.from(e));return F.wrap(r,t)}(t.headers,(null==i?void 0:i.onUpdateCookies)||(r?s:void 0));ed(t,e),o.mutableCookies=e}return o.mutableCookies},get draftMode(){return o.draftMode||(o.draftMode=new el(a,t,this.cookies,this.mutableCookies)),o.draftMode},reactLoadableManifest:(null==i?void 0:i.reactLoadableManifest)||{},assetPrefix:(null==i?void 0:i.assetPrefix)||""};return e.run(l,n,l)}};var eu=r(5303);function ep(){return{previewModeId:process.env.__NEXT_PREVIEW_MODE_ID,previewModeSigningKey:process.env.__NEXT_PREVIEW_MODE_SIGNING_KEY||"",previewModeEncryptionKey:process.env.__NEXT_PREVIEW_MODE_ENCRYPTION_KEY||""}}class em extends A.I{constructor(e){super(e.input,e.init),this.sourcePage=e.page}get request(){throw new b.qJ({page:this.sourcePage})}respondWith(){throw new b.qJ({page:this.sourcePage})}waitUntil(){throw new b.qJ({page:this.sourcePage})}}let eg={keys:e=>Array.from(e.keys()),get:(e,t)=>e.get(t)??void 0},eh=(e,t)=>es().withPropagatedContext(e.headers,t,eg),ef=!1;async function ey(e){let t,i;!function(){if(!ef&&(ef=!0,"true"===process.env.NEXT_PRIVATE_TEST_PROXY)){let{interceptTestApis:e,wrapRequestHandler:t}=r(4177);e(),eh=t(eh)}}(),await v();let n=void 0!==self.__BUILD_MANIFEST;e.request.url=e.request.url.replace(/\.rsc($|\?)/,"$1");let a=new L.c(e.request.url,{headers:e.request.headers,nextConfig:e.request.nextConfig});for(let e of[...a.searchParams.keys()]){let t=a.searchParams.getAll(e);(0,S.LI)(e,r=>{for(let e of(a.searchParams.delete(r),t))a.searchParams.append(r,e);a.searchParams.delete(e)})}let s=a.buildId;a.buildId="";let o=e.request.headers["x-nextjs-data"];o&&"/index"===a.pathname&&(a.pathname="/");let l=(0,S.EK)(e.request.headers),d=new Map;if(!n)for(let e of N){let t=e.toString().toLowerCase();l.get(t)&&(d.set(t,l.get(t)),l.delete(t))}let c=new em({page:e.page,input:(function(e,t){let r="string"==typeof e,i=r?new URL(e):e;for(let e of O)i.searchParams.delete(e);if(t)for(let e of k)i.searchParams.delete(e);return r?i.toString():i})(a,!0).toString(),init:{body:e.request.body,geo:e.request.geo,headers:l,ip:e.request.ip,method:e.request.method,nextConfig:e.request.nextConfig,signal:e.request.signal}});o&&Object.defineProperty(c,"__isData",{enumerable:!1,value:!0}),!globalThis.__incrementalCacheShared&&e.IncrementalCache&&(globalThis.__incrementalCache=new e.IncrementalCache({appDir:!0,fetchCache:!0,minimalMode:!0,fetchCacheKeyPrefix:"",dev:!1,requestHeaders:e.request.headers,requestProtocol:"https",getPrerenderManifest:()=>({version:-1,routes:{},dynamicRoutes:{},notFoundRoutes:[],preview:ep()})}));let u=new M({request:c,page:e.page});if((t=await eh(c,()=>"/middleware"===e.page||"/src/middleware"===e.page?es().trace(h.execute,{spanName:`middleware ${c.method} ${c.nextUrl.pathname}`,attributes:{"http.target":c.nextUrl.pathname,"http.method":c.method}},()=>ec.wrap(eu.F,{req:c,renderOpts:{onUpdateCookies:e=>{i=e},previewProps:ep()}},()=>e.handler(c,u))):e.handler(c,u)))&&!(t instanceof Response))throw TypeError("Expected an instance of Response to be returned");t&&i&&t.headers.set("set-cookie",i);let p=null==t?void 0:t.headers.get("x-middleware-rewrite");if(t&&p&&!n){let r=new L.c(p,{forceLocale:!0,headers:e.request.headers,nextConfig:e.request.nextConfig});r.host===c.nextUrl.host&&(r.buildId=s||r.buildId,t.headers.set("x-middleware-rewrite",String(r)));let i=E(String(r),String(a));o&&t.headers.set("x-nextjs-rewrite",i)}let m=null==t?void 0:t.headers.get("Location");if(t&&m&&!n){let r=new L.c(m,{forceLocale:!1,headers:e.request.headers,nextConfig:e.request.nextConfig});t=new Response(t.body,t),r.host===c.nextUrl.host&&(r.buildId=s||r.buildId,t.headers.set("Location",String(r))),o&&(t.headers.delete("Location"),t.headers.set("x-nextjs-redirect",E(String(r),String(a))))}let g=t||R.x.next(),f=g.headers.get("x-middleware-override-headers"),y=[];if(f){for(let[e,t]of d)g.headers.set(`x-middleware-request-${e}`,t),y.push(e);y.length>0&&g.headers.set("x-middleware-override-headers",f+","+y.join(","))}return{response:g,waitUntil:Promise.all(u[P]),fetchMetrics:c.fetchMetrics}}var e_=r(6124),ev=r(3142);let ew=["en","zh"];(0,ev.cF)(async({requestLocale:e})=>{let t=await e;return t&&ew.includes(t)||(t="zh"),{locale:t,messages:(await r(6002)(`./${t}.json`)).default,timeZone:"Asia/Shanghai",now:new Date,formats:{dateTime:{short:{day:"numeric",month:"short",year:"numeric"}},number:{precise:{maximumFractionDigits:5}},list:{enumeration:{style:"long",type:"conjunction"}}}}});let eb=(0,e_.Z)({locales:ew,defaultLocale:"zh",localePrefix:"always",localeDetection:!0}),eS={matcher:["/","/(zh|en)/:path*","/((?!_next|_vercel|.*\\..*).*)"]},eT={...f},ex=eT.middleware||eT.default,eP="/middleware";if("function"!=typeof ex)throw Error(`The Middleware "${eP}" must export a \`middleware\` or a \`default\` function`);function eC(e){return ey({...e,page:eP,handler:ex})}},1354:(e,t,r)=>{"use strict";function i(e,t,r){if(r||2==arguments.length)for(var i,n=0,a=t.length;n<a;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))}r.r(t),r.d(t,{LookupSupportedLocales:()=>h,ResolveLocale:()=>g,match:()=>f}),Object.create,Object.create;var n,a=("function"==typeof SuppressedError&&SuppressedError,{supplemental:{languageMatching:{"written-new":[{paradigmLocales:{_locales:"en en_GB es es_419 pt_BR pt_PT"}},{$enUS:{_value:"AS+CA+GU+MH+MP+PH+PR+UM+US+VI"}},{$cnsar:{_value:"HK+MO"}},{$americas:{_value:"019"}},{$maghreb:{_value:"MA+DZ+TN+LY+MR+EH"}},{no:{_desired:"nb",_distance:"1"}},{bs:{_desired:"hr",_distance:"4"}},{bs:{_desired:"sh",_distance:"4"}},{hr:{_desired:"sh",_distance:"4"}},{sr:{_desired:"sh",_distance:"4"}},{aa:{_desired:"ssy",_distance:"4"}},{de:{_desired:"gsw",_distance:"4",_oneway:"true"}},{de:{_desired:"lb",_distance:"4",_oneway:"true"}},{no:{_desired:"da",_distance:"8"}},{nb:{_desired:"da",_distance:"8"}},{ru:{_desired:"ab",_distance:"30",_oneway:"true"}},{en:{_desired:"ach",_distance:"30",_oneway:"true"}},{nl:{_desired:"af",_distance:"20",_oneway:"true"}},{en:{_desired:"ak",_distance:"30",_oneway:"true"}},{en:{_desired:"am",_distance:"30",_oneway:"true"}},{es:{_desired:"ay",_distance:"20",_oneway:"true"}},{ru:{_desired:"az",_distance:"30",_oneway:"true"}},{ur:{_desired:"bal",_distance:"20",_oneway:"true"}},{ru:{_desired:"be",_distance:"20",_oneway:"true"}},{en:{_desired:"bem",_distance:"30",_oneway:"true"}},{hi:{_desired:"bh",_distance:"30",_oneway:"true"}},{en:{_desired:"bn",_distance:"30",_oneway:"true"}},{zh:{_desired:"bo",_distance:"20",_oneway:"true"}},{fr:{_desired:"br",_distance:"20",_oneway:"true"}},{es:{_desired:"ca",_distance:"20",_oneway:"true"}},{fil:{_desired:"ceb",_distance:"30",_oneway:"true"}},{en:{_desired:"chr",_distance:"20",_oneway:"true"}},{ar:{_desired:"ckb",_distance:"30",_oneway:"true"}},{fr:{_desired:"co",_distance:"20",_oneway:"true"}},{fr:{_desired:"crs",_distance:"20",_oneway:"true"}},{sk:{_desired:"cs",_distance:"20"}},{en:{_desired:"cy",_distance:"20",_oneway:"true"}},{en:{_desired:"ee",_distance:"30",_oneway:"true"}},{en:{_desired:"eo",_distance:"30",_oneway:"true"}},{es:{_desired:"eu",_distance:"20",_oneway:"true"}},{da:{_desired:"fo",_distance:"20",_oneway:"true"}},{nl:{_desired:"fy",_distance:"20",_oneway:"true"}},{en:{_desired:"ga",_distance:"20",_oneway:"true"}},{en:{_desired:"gaa",_distance:"30",_oneway:"true"}},{en:{_desired:"gd",_distance:"20",_oneway:"true"}},{es:{_desired:"gl",_distance:"20",_oneway:"true"}},{es:{_desired:"gn",_distance:"20",_oneway:"true"}},{hi:{_desired:"gu",_distance:"30",_oneway:"true"}},{en:{_desired:"ha",_distance:"30",_oneway:"true"}},{en:{_desired:"haw",_distance:"20",_oneway:"true"}},{fr:{_desired:"ht",_distance:"20",_oneway:"true"}},{ru:{_desired:"hy",_distance:"30",_oneway:"true"}},{en:{_desired:"ia",_distance:"30",_oneway:"true"}},{en:{_desired:"ig",_distance:"30",_oneway:"true"}},{en:{_desired:"is",_distance:"20",_oneway:"true"}},{id:{_desired:"jv",_distance:"20",_oneway:"true"}},{en:{_desired:"ka",_distance:"30",_oneway:"true"}},{fr:{_desired:"kg",_distance:"30",_oneway:"true"}},{ru:{_desired:"kk",_distance:"30",_oneway:"true"}},{en:{_desired:"km",_distance:"30",_oneway:"true"}},{en:{_desired:"kn",_distance:"30",_oneway:"true"}},{en:{_desired:"kri",_distance:"30",_oneway:"true"}},{tr:{_desired:"ku",_distance:"30",_oneway:"true"}},{ru:{_desired:"ky",_distance:"30",_oneway:"true"}},{it:{_desired:"la",_distance:"20",_oneway:"true"}},{en:{_desired:"lg",_distance:"30",_oneway:"true"}},{fr:{_desired:"ln",_distance:"30",_oneway:"true"}},{en:{_desired:"lo",_distance:"30",_oneway:"true"}},{en:{_desired:"loz",_distance:"30",_oneway:"true"}},{fr:{_desired:"lua",_distance:"30",_oneway:"true"}},{hi:{_desired:"mai",_distance:"20",_oneway:"true"}},{en:{_desired:"mfe",_distance:"30",_oneway:"true"}},{fr:{_desired:"mg",_distance:"30",_oneway:"true"}},{en:{_desired:"mi",_distance:"20",_oneway:"true"}},{en:{_desired:"ml",_distance:"30",_oneway:"true"}},{ru:{_desired:"mn",_distance:"30",_oneway:"true"}},{hi:{_desired:"mr",_distance:"30",_oneway:"true"}},{id:{_desired:"ms",_distance:"30",_oneway:"true"}},{en:{_desired:"mt",_distance:"30",_oneway:"true"}},{en:{_desired:"my",_distance:"30",_oneway:"true"}},{en:{_desired:"ne",_distance:"30",_oneway:"true"}},{nb:{_desired:"nn",_distance:"20"}},{no:{_desired:"nn",_distance:"20"}},{en:{_desired:"nso",_distance:"30",_oneway:"true"}},{en:{_desired:"ny",_distance:"30",_oneway:"true"}},{en:{_desired:"nyn",_distance:"30",_oneway:"true"}},{fr:{_desired:"oc",_distance:"20",_oneway:"true"}},{en:{_desired:"om",_distance:"30",_oneway:"true"}},{en:{_desired:"or",_distance:"30",_oneway:"true"}},{en:{_desired:"pa",_distance:"30",_oneway:"true"}},{en:{_desired:"pcm",_distance:"20",_oneway:"true"}},{en:{_desired:"ps",_distance:"30",_oneway:"true"}},{es:{_desired:"qu",_distance:"30",_oneway:"true"}},{de:{_desired:"rm",_distance:"20",_oneway:"true"}},{en:{_desired:"rn",_distance:"30",_oneway:"true"}},{fr:{_desired:"rw",_distance:"30",_oneway:"true"}},{hi:{_desired:"sa",_distance:"30",_oneway:"true"}},{en:{_desired:"sd",_distance:"30",_oneway:"true"}},{en:{_desired:"si",_distance:"30",_oneway:"true"}},{en:{_desired:"sn",_distance:"30",_oneway:"true"}},{en:{_desired:"so",_distance:"30",_oneway:"true"}},{en:{_desired:"sq",_distance:"30",_oneway:"true"}},{en:{_desired:"st",_distance:"30",_oneway:"true"}},{id:{_desired:"su",_distance:"20",_oneway:"true"}},{en:{_desired:"sw",_distance:"30",_oneway:"true"}},{en:{_desired:"ta",_distance:"30",_oneway:"true"}},{en:{_desired:"te",_distance:"30",_oneway:"true"}},{ru:{_desired:"tg",_distance:"30",_oneway:"true"}},{en:{_desired:"ti",_distance:"30",_oneway:"true"}},{ru:{_desired:"tk",_distance:"30",_oneway:"true"}},{en:{_desired:"tlh",_distance:"30",_oneway:"true"}},{en:{_desired:"tn",_distance:"30",_oneway:"true"}},{en:{_desired:"to",_distance:"30",_oneway:"true"}},{ru:{_desired:"tt",_distance:"30",_oneway:"true"}},{en:{_desired:"tum",_distance:"30",_oneway:"true"}},{zh:{_desired:"ug",_distance:"20",_oneway:"true"}},{ru:{_desired:"uk",_distance:"20",_oneway:"true"}},{en:{_desired:"ur",_distance:"30",_oneway:"true"}},{ru:{_desired:"uz",_distance:"30",_oneway:"true"}},{fr:{_desired:"wo",_distance:"30",_oneway:"true"}},{en:{_desired:"xh",_distance:"30",_oneway:"true"}},{en:{_desired:"yi",_distance:"30",_oneway:"true"}},{en:{_desired:"yo",_distance:"30",_oneway:"true"}},{zh:{_desired:"za",_distance:"20",_oneway:"true"}},{en:{_desired:"zu",_distance:"30",_oneway:"true"}},{ar:{_desired:"aao",_distance:"10",_oneway:"true"}},{ar:{_desired:"abh",_distance:"10",_oneway:"true"}},{ar:{_desired:"abv",_distance:"10",_oneway:"true"}},{ar:{_desired:"acm",_distance:"10",_oneway:"true"}},{ar:{_desired:"acq",_distance:"10",_oneway:"true"}},{ar:{_desired:"acw",_distance:"10",_oneway:"true"}},{ar:{_desired:"acx",_distance:"10",_oneway:"true"}},{ar:{_desired:"acy",_distance:"10",_oneway:"true"}},{ar:{_desired:"adf",_distance:"10",_oneway:"true"}},{ar:{_desired:"aeb",_distance:"10",_oneway:"true"}},{ar:{_desired:"aec",_distance:"10",_oneway:"true"}},{ar:{_desired:"afb",_distance:"10",_oneway:"true"}},{ar:{_desired:"ajp",_distance:"10",_oneway:"true"}},{ar:{_desired:"apc",_distance:"10",_oneway:"true"}},{ar:{_desired:"apd",_distance:"10",_oneway:"true"}},{ar:{_desired:"arq",_distance:"10",_oneway:"true"}},{ar:{_desired:"ars",_distance:"10",_oneway:"true"}},{ar:{_desired:"ary",_distance:"10",_oneway:"true"}},{ar:{_desired:"arz",_distance:"10",_oneway:"true"}},{ar:{_desired:"auz",_distance:"10",_oneway:"true"}},{ar:{_desired:"avl",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayh",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayl",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayn",_distance:"10",_oneway:"true"}},{ar:{_desired:"ayp",_distance:"10",_oneway:"true"}},{ar:{_desired:"bbz",_distance:"10",_oneway:"true"}},{ar:{_desired:"pga",_distance:"10",_oneway:"true"}},{ar:{_desired:"shu",_distance:"10",_oneway:"true"}},{ar:{_desired:"ssh",_distance:"10",_oneway:"true"}},{az:{_desired:"azb",_distance:"10",_oneway:"true"}},{et:{_desired:"vro",_distance:"10",_oneway:"true"}},{ff:{_desired:"ffm",_distance:"10",_oneway:"true"}},{ff:{_desired:"fub",_distance:"10",_oneway:"true"}},{ff:{_desired:"fue",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuf",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuh",_distance:"10",_oneway:"true"}},{ff:{_desired:"fui",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuq",_distance:"10",_oneway:"true"}},{ff:{_desired:"fuv",_distance:"10",_oneway:"true"}},{gn:{_desired:"gnw",_distance:"10",_oneway:"true"}},{gn:{_desired:"gui",_distance:"10",_oneway:"true"}},{gn:{_desired:"gun",_distance:"10",_oneway:"true"}},{gn:{_desired:"nhd",_distance:"10",_oneway:"true"}},{iu:{_desired:"ikt",_distance:"10",_oneway:"true"}},{kln:{_desired:"enb",_distance:"10",_oneway:"true"}},{kln:{_desired:"eyo",_distance:"10",_oneway:"true"}},{kln:{_desired:"niq",_distance:"10",_oneway:"true"}},{kln:{_desired:"oki",_distance:"10",_oneway:"true"}},{kln:{_desired:"pko",_distance:"10",_oneway:"true"}},{kln:{_desired:"sgc",_distance:"10",_oneway:"true"}},{kln:{_desired:"tec",_distance:"10",_oneway:"true"}},{kln:{_desired:"tuy",_distance:"10",_oneway:"true"}},{kok:{_desired:"gom",_distance:"10",_oneway:"true"}},{kpe:{_desired:"gkp",_distance:"10",_oneway:"true"}},{luy:{_desired:"ida",_distance:"10",_oneway:"true"}},{luy:{_desired:"lkb",_distance:"10",_oneway:"true"}},{luy:{_desired:"lko",_distance:"10",_oneway:"true"}},{luy:{_desired:"lks",_distance:"10",_oneway:"true"}},{luy:{_desired:"lri",_distance:"10",_oneway:"true"}},{luy:{_desired:"lrm",_distance:"10",_oneway:"true"}},{luy:{_desired:"lsm",_distance:"10",_oneway:"true"}},{luy:{_desired:"lto",_distance:"10",_oneway:"true"}},{luy:{_desired:"lts",_distance:"10",_oneway:"true"}},{luy:{_desired:"lwg",_distance:"10",_oneway:"true"}},{luy:{_desired:"nle",_distance:"10",_oneway:"true"}},{luy:{_desired:"nyd",_distance:"10",_oneway:"true"}},{luy:{_desired:"rag",_distance:"10",_oneway:"true"}},{lv:{_desired:"ltg",_distance:"10",_oneway:"true"}},{mg:{_desired:"bhr",_distance:"10",_oneway:"true"}},{mg:{_desired:"bjq",_distance:"10",_oneway:"true"}},{mg:{_desired:"bmm",_distance:"10",_oneway:"true"}},{mg:{_desired:"bzc",_distance:"10",_oneway:"true"}},{mg:{_desired:"msh",_distance:"10",_oneway:"true"}},{mg:{_desired:"skg",_distance:"10",_oneway:"true"}},{mg:{_desired:"tdx",_distance:"10",_oneway:"true"}},{mg:{_desired:"tkg",_distance:"10",_oneway:"true"}},{mg:{_desired:"txy",_distance:"10",_oneway:"true"}},{mg:{_desired:"xmv",_distance:"10",_oneway:"true"}},{mg:{_desired:"xmw",_distance:"10",_oneway:"true"}},{mn:{_desired:"mvf",_distance:"10",_oneway:"true"}},{ms:{_desired:"bjn",_distance:"10",_oneway:"true"}},{ms:{_desired:"btj",_distance:"10",_oneway:"true"}},{ms:{_desired:"bve",_distance:"10",_oneway:"true"}},{ms:{_desired:"bvu",_distance:"10",_oneway:"true"}},{ms:{_desired:"coa",_distance:"10",_oneway:"true"}},{ms:{_desired:"dup",_distance:"10",_oneway:"true"}},{ms:{_desired:"hji",_distance:"10",_oneway:"true"}},{ms:{_desired:"id",_distance:"10",_oneway:"true"}},{ms:{_desired:"jak",_distance:"10",_oneway:"true"}},{ms:{_desired:"jax",_distance:"10",_oneway:"true"}},{ms:{_desired:"kvb",_distance:"10",_oneway:"true"}},{ms:{_desired:"kvr",_distance:"10",_oneway:"true"}},{ms:{_desired:"kxd",_distance:"10",_oneway:"true"}},{ms:{_desired:"lce",_distance:"10",_oneway:"true"}},{ms:{_desired:"lcf",_distance:"10",_oneway:"true"}},{ms:{_desired:"liw",_distance:"10",_oneway:"true"}},{ms:{_desired:"max",_distance:"10",_oneway:"true"}},{ms:{_desired:"meo",_distance:"10",_oneway:"true"}},{ms:{_desired:"mfa",_distance:"10",_oneway:"true"}},{ms:{_desired:"mfb",_distance:"10",_oneway:"true"}},{ms:{_desired:"min",_distance:"10",_oneway:"true"}},{ms:{_desired:"mqg",_distance:"10",_oneway:"true"}},{ms:{_desired:"msi",_distance:"10",_oneway:"true"}},{ms:{_desired:"mui",_distance:"10",_oneway:"true"}},{ms:{_desired:"orn",_distance:"10",_oneway:"true"}},{ms:{_desired:"ors",_distance:"10",_oneway:"true"}},{ms:{_desired:"pel",_distance:"10",_oneway:"true"}},{ms:{_desired:"pse",_distance:"10",_oneway:"true"}},{ms:{_desired:"tmw",_distance:"10",_oneway:"true"}},{ms:{_desired:"urk",_distance:"10",_oneway:"true"}},{ms:{_desired:"vkk",_distance:"10",_oneway:"true"}},{ms:{_desired:"vkt",_distance:"10",_oneway:"true"}},{ms:{_desired:"xmm",_distance:"10",_oneway:"true"}},{ms:{_desired:"zlm",_distance:"10",_oneway:"true"}},{ms:{_desired:"zmi",_distance:"10",_oneway:"true"}},{ne:{_desired:"dty",_distance:"10",_oneway:"true"}},{om:{_desired:"gax",_distance:"10",_oneway:"true"}},{om:{_desired:"hae",_distance:"10",_oneway:"true"}},{om:{_desired:"orc",_distance:"10",_oneway:"true"}},{or:{_desired:"spv",_distance:"10",_oneway:"true"}},{ps:{_desired:"pbt",_distance:"10",_oneway:"true"}},{ps:{_desired:"pst",_distance:"10",_oneway:"true"}},{qu:{_desired:"qub",_distance:"10",_oneway:"true"}},{qu:{_desired:"qud",_distance:"10",_oneway:"true"}},{qu:{_desired:"quf",_distance:"10",_oneway:"true"}},{qu:{_desired:"qug",_distance:"10",_oneway:"true"}},{qu:{_desired:"quh",_distance:"10",_oneway:"true"}},{qu:{_desired:"quk",_distance:"10",_oneway:"true"}},{qu:{_desired:"qul",_distance:"10",_oneway:"true"}},{qu:{_desired:"qup",_distance:"10",_oneway:"true"}},{qu:{_desired:"qur",_distance:"10",_oneway:"true"}},{qu:{_desired:"qus",_distance:"10",_oneway:"true"}},{qu:{_desired:"quw",_distance:"10",_oneway:"true"}},{qu:{_desired:"qux",_distance:"10",_oneway:"true"}},{qu:{_desired:"quy",_distance:"10",_oneway:"true"}},{qu:{_desired:"qva",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qve",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvi",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvj",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvl",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvm",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvn",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvo",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvp",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvs",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvw",_distance:"10",_oneway:"true"}},{qu:{_desired:"qvz",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwa",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qwh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qws",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxa",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxc",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxh",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxl",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxn",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxo",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxp",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxr",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxt",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxu",_distance:"10",_oneway:"true"}},{qu:{_desired:"qxw",_distance:"10",_oneway:"true"}},{sc:{_desired:"sdc",_distance:"10",_oneway:"true"}},{sc:{_desired:"sdn",_distance:"10",_oneway:"true"}},{sc:{_desired:"sro",_distance:"10",_oneway:"true"}},{sq:{_desired:"aae",_distance:"10",_oneway:"true"}},{sq:{_desired:"aat",_distance:"10",_oneway:"true"}},{sq:{_desired:"aln",_distance:"10",_oneway:"true"}},{syr:{_desired:"aii",_distance:"10",_oneway:"true"}},{uz:{_desired:"uzs",_distance:"10",_oneway:"true"}},{yi:{_desired:"yih",_distance:"10",_oneway:"true"}},{zh:{_desired:"cdo",_distance:"10",_oneway:"true"}},{zh:{_desired:"cjy",_distance:"10",_oneway:"true"}},{zh:{_desired:"cpx",_distance:"10",_oneway:"true"}},{zh:{_desired:"czh",_distance:"10",_oneway:"true"}},{zh:{_desired:"czo",_distance:"10",_oneway:"true"}},{zh:{_desired:"gan",_distance:"10",_oneway:"true"}},{zh:{_desired:"hak",_distance:"10",_oneway:"true"}},{zh:{_desired:"hsn",_distance:"10",_oneway:"true"}},{zh:{_desired:"lzh",_distance:"10",_oneway:"true"}},{zh:{_desired:"mnp",_distance:"10",_oneway:"true"}},{zh:{_desired:"nan",_distance:"10",_oneway:"true"}},{zh:{_desired:"wuu",_distance:"10",_oneway:"true"}},{zh:{_desired:"yue",_distance:"10",_oneway:"true"}},{"*":{_desired:"*",_distance:"80"}},{"en-Latn":{_desired:"am-Ethi",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"az-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"bn-Beng",_distance:"10",_oneway:"true"}},{"zh-Hans":{_desired:"bo-Tibt",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"hy-Armn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ka-Geor",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"km-Khmr",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"kn-Knda",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"lo-Laoo",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ml-Mlym",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"my-Mymr",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ne-Deva",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"or-Orya",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"pa-Guru",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ps-Arab",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"sd-Arab",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"si-Sinh",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ta-Taml",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"te-Telu",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ti-Ethi",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"tk-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"ur-Arab",_distance:"10",_oneway:"true"}},{"ru-Cyrl":{_desired:"uz-Latn",_distance:"10",_oneway:"true"}},{"en-Latn":{_desired:"yi-Hebr",_distance:"10",_oneway:"true"}},{"sr-Cyrl":{_desired:"sr-Latn",_distance:"5"}},{"zh-Hans":{_desired:"za-Latn",_distance:"10",_oneway:"true"}},{"zh-Hans":{_desired:"zh-Hani",_distance:"20",_oneway:"true"}},{"zh-Hant":{_desired:"zh-Hani",_distance:"20",_oneway:"true"}},{"ar-Arab":{_desired:"ar-Latn",_distance:"20",_oneway:"true"}},{"bn-Beng":{_desired:"bn-Latn",_distance:"20",_oneway:"true"}},{"gu-Gujr":{_desired:"gu-Latn",_distance:"20",_oneway:"true"}},{"hi-Deva":{_desired:"hi-Latn",_distance:"20",_oneway:"true"}},{"kn-Knda":{_desired:"kn-Latn",_distance:"20",_oneway:"true"}},{"ml-Mlym":{_desired:"ml-Latn",_distance:"20",_oneway:"true"}},{"mr-Deva":{_desired:"mr-Latn",_distance:"20",_oneway:"true"}},{"ta-Taml":{_desired:"ta-Latn",_distance:"20",_oneway:"true"}},{"te-Telu":{_desired:"te-Latn",_distance:"20",_oneway:"true"}},{"zh-Hans":{_desired:"zh-Latn",_distance:"20",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Latn",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hani",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hira",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Kana",_distance:"5",_oneway:"true"}},{"ja-Jpan":{_desired:"ja-Hrkt",_distance:"5",_oneway:"true"}},{"ja-Hrkt":{_desired:"ja-Hira",_distance:"5",_oneway:"true"}},{"ja-Hrkt":{_desired:"ja-Kana",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Hani",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Hang",_distance:"5",_oneway:"true"}},{"ko-Kore":{_desired:"ko-Jamo",_distance:"5",_oneway:"true"}},{"ko-Hang":{_desired:"ko-Jamo",_distance:"5",_oneway:"true"}},{"*-*":{_desired:"*-*",_distance:"50"}},{"ar-*-$maghreb":{_desired:"ar-*-$maghreb",_distance:"4"}},{"ar-*-$!maghreb":{_desired:"ar-*-$!maghreb",_distance:"4"}},{"ar-*-*":{_desired:"ar-*-*",_distance:"5"}},{"en-*-$enUS":{_desired:"en-*-$enUS",_distance:"4"}},{"en-*-GB":{_desired:"en-*-$!enUS",_distance:"3"}},{"en-*-$!enUS":{_desired:"en-*-$!enUS",_distance:"4"}},{"en-*-*":{_desired:"en-*-*",_distance:"5"}},{"es-*-$americas":{_desired:"es-*-$americas",_distance:"4"}},{"es-*-$!americas":{_desired:"es-*-$!americas",_distance:"4"}},{"es-*-*":{_desired:"es-*-*",_distance:"5"}},{"pt-*-$americas":{_desired:"pt-*-$americas",_distance:"4"}},{"pt-*-$!americas":{_desired:"pt-*-$!americas",_distance:"4"}},{"pt-*-*":{_desired:"pt-*-*",_distance:"5"}},{"zh-Hant-$cnsar":{_desired:"zh-Hant-$cnsar",_distance:"4"}},{"zh-Hant-$!cnsar":{_desired:"zh-Hant-$!cnsar",_distance:"4"}},{"zh-Hant-*":{_desired:"zh-Hant-*",_distance:"5"}},{"*-*-*":{_desired:"*-*-*",_distance:"4"}}]}}}),s={"001":["001","001-status-grouping","002","005","009","011","013","014","015","017","018","019","021","029","030","034","035","039","053","054","057","061","142","143","145","150","151","154","155","AC","AD","AE","AF","AG","AI","AL","AM","AO","AQ","AR","AS","AT","AU","AW","AX","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BL","BM","BN","BO","BQ","BR","BS","BT","BV","BW","BY","BZ","CA","CC","CD","CF","CG","CH","CI","CK","CL","CM","CN","CO","CP","CQ","CR","CU","CV","CW","CX","CY","CZ","DE","DG","DJ","DK","DM","DO","DZ","EA","EC","EE","EG","EH","ER","ES","ET","EU","EZ","FI","FJ","FK","FM","FO","FR","GA","GB","GD","GE","GF","GG","GH","GI","GL","GM","GN","GP","GQ","GR","GS","GT","GU","GW","GY","HK","HM","HN","HR","HT","HU","IC","ID","IE","IL","IM","IN","IO","IQ","IR","IS","IT","JE","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KY","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MF","MG","MH","MK","ML","MM","MN","MO","MP","MQ","MR","MS","MT","MU","MV","MW","MX","MY","MZ","NA","NC","NE","NF","NG","NI","NL","NO","NP","NR","NU","NZ","OM","PA","PE","PF","PG","PH","PK","PL","PM","PN","PR","PS","PT","PW","PY","QA","QO","RE","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SH","SI","SJ","SK","SL","SM","SN","SO","SR","SS","ST","SV","SX","SY","SZ","TA","TC","TD","TF","TG","TH","TJ","TK","TL","TM","TN","TO","TR","TT","TV","TW","TZ","UA","UG","UM","UN","US","UY","UZ","VA","VC","VE","VG","VI","VN","VU","WF","WS","XK","YE","YT","ZA","ZM","ZW"],"002":["002","002-status-grouping","011","014","015","017","018","202","AO","BF","BI","BJ","BW","CD","CF","CG","CI","CM","CV","DJ","DZ","EA","EG","EH","ER","ET","GA","GH","GM","GN","GQ","GW","IC","IO","KE","KM","LR","LS","LY","MA","MG","ML","MR","MU","MW","MZ","NA","NE","NG","RE","RW","SC","SD","SH","SL","SN","SO","SS","ST","SZ","TD","TF","TG","TN","TZ","UG","YT","ZA","ZM","ZW"],"003":["003","013","021","029","AG","AI","AW","BB","BL","BM","BQ","BS","BZ","CA","CR","CU","CW","DM","DO","GD","GL","GP","GT","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PM","PR","SV","SX","TC","TT","US","VC","VG","VI"],"005":["005","AR","BO","BR","BV","CL","CO","EC","FK","GF","GS","GY","PE","PY","SR","UY","VE"],"009":["009","053","054","057","061","AC","AQ","AS","AU","CC","CK","CP","CX","DG","FJ","FM","GU","HM","KI","MH","MP","NC","NF","NR","NU","NZ","PF","PG","PN","PW","QO","SB","TA","TK","TO","TV","UM","VU","WF","WS"],"011":["011","BF","BJ","CI","CV","GH","GM","GN","GW","LR","ML","MR","NE","NG","SH","SL","SN","TG"],"013":["013","BZ","CR","GT","HN","MX","NI","PA","SV"],"014":["014","BI","DJ","ER","ET","IO","KE","KM","MG","MU","MW","MZ","RE","RW","SC","SO","SS","TF","TZ","UG","YT","ZM","ZW"],"015":["015","DZ","EA","EG","EH","IC","LY","MA","SD","TN"],"017":["017","AO","CD","CF","CG","CM","GA","GQ","ST","TD"],"018":["018","BW","LS","NA","SZ","ZA"],"019":["003","005","013","019","019-status-grouping","021","029","419","AG","AI","AR","AW","BB","BL","BM","BO","BQ","BR","BS","BV","BZ","CA","CL","CO","CR","CU","CW","DM","DO","EC","FK","GD","GF","GL","GP","GS","GT","GY","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PE","PM","PR","PY","SR","SV","SX","TC","TT","US","UY","VC","VE","VG","VI"],"021":["021","BM","CA","GL","PM","US"],"029":["029","AG","AI","AW","BB","BL","BQ","BS","CU","CW","DM","DO","GD","GP","HT","JM","KN","KY","LC","MF","MQ","MS","PR","SX","TC","TT","VC","VG","VI"],"030":["030","CN","HK","JP","KP","KR","MN","MO","TW"],"034":["034","AF","BD","BT","IN","IR","LK","MV","NP","PK"],"035":["035","BN","ID","KH","LA","MM","MY","PH","SG","TH","TL","VN"],"039":["039","AD","AL","BA","ES","GI","GR","HR","IT","ME","MK","MT","PT","RS","SI","SM","VA","XK"],"053":["053","AU","CC","CX","HM","NF","NZ"],"054":["054","FJ","NC","PG","SB","VU"],"057":["057","FM","GU","KI","MH","MP","NR","PW","UM"],"061":["061","AS","CK","NU","PF","PN","TK","TO","TV","WF","WS"],142:["030","034","035","142","143","145","AE","AF","AM","AZ","BD","BH","BN","BT","CN","CY","GE","HK","ID","IL","IN","IQ","IR","JO","JP","KG","KH","KP","KR","KW","KZ","LA","LB","LK","MM","MN","MO","MV","MY","NP","OM","PH","PK","PS","QA","SA","SG","SY","TH","TJ","TL","TM","TR","TW","UZ","VN","YE"],143:["143","KG","KZ","TJ","TM","UZ"],145:["145","AE","AM","AZ","BH","CY","GE","IL","IQ","JO","KW","LB","OM","PS","QA","SA","SY","TR","YE"],150:["039","150","151","154","155","AD","AL","AT","AX","BA","BE","BG","BY","CH","CQ","CZ","DE","DK","EE","ES","FI","FO","FR","GB","GG","GI","GR","HR","HU","IE","IM","IS","IT","JE","LI","LT","LU","LV","MC","MD","ME","MK","MT","NL","NO","PL","PT","RO","RS","RU","SE","SI","SJ","SK","SM","UA","VA","XK"],151:["151","BG","BY","CZ","HU","MD","PL","RO","RU","SK","UA"],154:["154","AX","CQ","DK","EE","FI","FO","GB","GG","IE","IM","IS","JE","LT","LV","NO","SE","SJ"],155:["155","AT","BE","CH","DE","FR","LI","LU","MC","NL"],202:["011","014","017","018","202","AO","BF","BI","BJ","BW","CD","CF","CG","CI","CM","CV","DJ","ER","ET","GA","GH","GM","GN","GQ","GW","IO","KE","KM","LR","LS","MG","ML","MR","MU","MW","MZ","NA","NE","NG","RE","RW","SC","SH","SL","SN","SO","SS","ST","SZ","TD","TF","TG","TZ","UG","YT","ZA","ZM","ZW"],419:["005","013","029","419","AG","AI","AR","AW","BB","BL","BO","BQ","BR","BS","BV","BZ","CL","CO","CR","CU","CW","DM","DO","EC","FK","GD","GF","GP","GS","GT","GY","HN","HT","JM","KN","KY","LC","MF","MQ","MS","MX","NI","PA","PE","PR","PY","SR","SV","SX","TC","TT","UY","VC","VE","VG","VI"],EU:["AT","BE","BG","CY","CZ","DE","DK","EE","ES","EU","FI","FR","GR","HR","HU","IE","IT","LT","LU","LV","MT","NL","PL","PT","RO","SE","SI","SK"],EZ:["AT","BE","CY","DE","EE","ES","EZ","FI","FR","GR","IE","IT","LT","LU","LV","MT","NL","PT","SI","SK"],QO:["AC","AQ","CP","DG","QO","TA"],UN:["AD","AE","AF","AG","AL","AM","AO","AR","AT","AU","AZ","BA","BB","BD","BE","BF","BG","BH","BI","BJ","BN","BO","BR","BS","BT","BW","BY","BZ","CA","CD","CF","CG","CH","CI","CL","CM","CN","CO","CR","CU","CV","CY","CZ","DE","DJ","DK","DM","DO","DZ","EC","EE","EG","ER","ES","ET","FI","FJ","FM","FR","GA","GB","GD","GE","GH","GM","GN","GQ","GR","GT","GW","GY","HN","HR","HT","HU","ID","IE","IL","IN","IQ","IR","IS","IT","JM","JO","JP","KE","KG","KH","KI","KM","KN","KP","KR","KW","KZ","LA","LB","LC","LI","LK","LR","LS","LT","LU","LV","LY","MA","MC","MD","ME","MG","MH","MK","ML","MM","MN","MR","MT","MU","MV","MW","MX","MY","MZ","NA","NE","NG","NI","NL","NO","NP","NR","NZ","OM","PA","PE","PG","PH","PK","PL","PT","PW","PY","QA","RO","RS","RU","RW","SA","SB","SC","SD","SE","SG","SI","SK","SL","SM","SN","SO","SR","SS","ST","SV","SY","SZ","TD","TG","TH","TJ","TL","TM","TN","TO","TR","TT","TV","TZ","UA","UG","UN","US","UY","UZ","VC","VE","VN","VU","WS","YE","ZA","ZM","ZW"]},o=/-u(?:-[0-9a-z]{2,8})+/gi;function l(e,t,r){if(void 0===r&&(r=Error),!e)throw new r(t)}function d(e,t,r){var n=t.split("-"),a=n[0],o=n[1],l=n[2],d=!0;if(l&&"$"===l[0]){var c="!"!==l[1],u=(c?r[l.slice(1)]:r[l.slice(2)]).map(function(e){return s[e]||[e]}).reduce(function(e,t){return i(i([],e,!0),t,!0)},[]);d&&(d=!(u.indexOf(e.region||"")>1!=c))}else d&&(d=!e.region||"*"===l||l===e.region);return d&&(d=!e.script||"*"===o||o===e.script),d&&(d=!e.language||"*"===a||a===e.language),d}function c(e){return[e.language,e.script,e.region].filter(Boolean).join("-")}function u(e,t,r){for(var i=0,n=r.matches;i<n.length;i++){var a=n[i],s=d(e,a.desired,r.matchVariables)&&d(t,a.supported,r.matchVariables);if(a.oneway||s||(s=d(e,a.supported,r.matchVariables)&&d(t,a.desired,r.matchVariables)),s){var o=10*a.distance;if(r.paradigmLocales.indexOf(c(e))>-1!=r.paradigmLocales.indexOf(c(t))>-1)return o-1;return o}}throw Error("No matching distance found")}function p(e){return Intl.getCanonicalLocales(e)[0]}function m(e,t){for(var r=t;;){if(e.indexOf(r)>-1)return r;var i=r.lastIndexOf("-");if(!~i)return;i>=2&&"-"===r[i-2]&&(i-=2),r=r.slice(0,i)}}function g(e,t,r,s,d,c){"lookup"===r.localeMatcher?h=function(e,t,r){for(var i={locale:""},n=0;n<t.length;n++){var a=t[n],s=a.replace(o,""),l=m(e,s);if(l)return i.locale=l,a!==s&&(i.extension=a.slice(s.length,a.length)),i}return i.locale=r(),i}(Array.from(e),t,c):(y=Array.from(e),w=[],b=t.reduce(function(e,t){var r=t.replace(o,"");return w.push(r),e[r]=t,e},{}),(void 0===S&&(S=838),T=1/0,x={matchedDesiredLocale:"",distances:{}},w.forEach(function(e,t){x.distances[e]||(x.distances[e]={}),y.forEach(function(r){var s,o,l,d,c,p,m=(s=new Intl.Locale(e).maximize(),o=new Intl.Locale(r).maximize(),l={language:s.language,script:s.script||"",region:s.region||""},d={language:o.language,script:o.script||"",region:o.region||""},c=0,p=function(){var e,t;if(!n){var r=null===(t=null===(e=a.supplemental.languageMatching["written-new"][0])||void 0===e?void 0:e.paradigmLocales)||void 0===t?void 0:t._locales.split(" "),s=a.supplemental.languageMatching["written-new"].slice(1,5);n={matches:a.supplemental.languageMatching["written-new"].slice(5).map(function(e){var t=Object.keys(e)[0],r=e[t];return{supported:t,desired:r._desired,distance:+r._distance,oneway:"true"===r.oneway}},{}),matchVariables:s.reduce(function(e,t){var r=Object.keys(t)[0],i=t[r];return e[r.slice(1)]=i._value.split("+"),e},{}),paradigmLocales:i(i([],r,!0),r.map(function(e){return new Intl.Locale(e.replace(/_/g,"-")).maximize().toString()}),!0)}}return n}(),l.language!==d.language&&(c+=u({language:s.language,script:"",region:""},{language:o.language,script:"",region:""},p)),l.script!==d.script&&(c+=u({language:s.language,script:l.script,region:""},{language:o.language,script:l.script,region:""},p)),l.region!==d.region&&(c+=u(l,d,p)),c+0+40*t);x.distances[e][r]=m,m<T&&(T=m,x.matchedDesiredLocale=e,x.matchedSupportedLocale=r)})}),T>=S&&(x.matchedDesiredLocale=void 0,x.matchedSupportedLocale=void 0),x).matchedSupportedLocale&&x.matchedDesiredLocale&&(_=x.matchedSupportedLocale,v=b[x.matchedDesiredLocale].slice(x.matchedDesiredLocale.length)||void 0),h=_?{locale:_,extension:v}:{locale:c()}),null==h&&(h={locale:c(),extension:""});var g,h,f,y,_,v,w,b,S,T,x,P=h.locale,C=d[P],M={locale:"en",dataLocale:P};f=h.extension?function(e){l(e===e.toLowerCase(),"Expected extension to be lowercase"),l("-u-"===e.slice(0,3),"Expected extension to be a Unicode locale extension");for(var t,r=[],i=[],n=e.length,a=3;a<n;){var s=e.indexOf("-",a),o=void 0;o=-1===s?n-a:s-a;var d=e.slice(a,a+o);l(o>=2,"Expected a subtag to have at least 2 characters"),void 0===t&&2!=o?-1===r.indexOf(d)&&r.push(d):2===o?(t={key:d,value:""},void 0===i.find(function(e){return e.key===(null==t?void 0:t.key)})&&i.push(t)):(null==t?void 0:t.value)===""?t.value=d:(l(void 0!==t,"Expected keyword to be defined"),t.value+="-"+d),a+=o+1}return{attributes:r,keywords:i}}(h.extension).keywords:[];for(var A=[],R=function(e){var t,i,n=null!==(g=null==C?void 0:C[e])&&void 0!==g?g:[];l(Array.isArray(n),"keyLocaleData for ".concat(e," must be an array"));var a=n[0];l(void 0===a||"string"==typeof a,"value must be a string or undefined");var s=void 0,o=f.find(function(t){return t.key===e});if(o){var d=o.value;""!==d?n.indexOf(d)>-1&&(s={key:e,value:a=d}):n.indexOf("true")>-1&&(s={key:e,value:a="true"})}var c=r[e];l(null==c||"string"==typeof c,"optionsValue must be a string or undefined"),"string"==typeof c&&(t=e.toLowerCase(),i=c.toLowerCase(),l(void 0!==t,"ukey must be defined"),""===(c=i)&&(c="true")),c!==a&&n.indexOf(c)>-1&&(a=c,s=void 0),s&&A.push(s),M[e]=a},E=0;E<s.length;E++)R(s[E]);return A.length>0&&(P=function(e,t,r){l(-1===e.indexOf("-u-"),"Expected locale to not have a Unicode locale extension");for(var i="-u",n=0;n<t.length;n++){var a=t[n];i+="-".concat(a)}for(var s=0;s<r.length;s++){var o=r[s],d=o.key,c=o.value;i+="-".concat(d),""!==c&&(i+="-".concat(c))}if("-u"===i)return p(e);var u=e.indexOf("-x-");return p(-1===u?e+i:e.slice(0,u)+i+e.slice(u))}(P,[],A)),M.locale=P,M}function h(e,t){for(var r=[],i=0;i<t.length;i++){var n=m(e,t[i].replace(o,""));n&&r.push(n)}return r}function f(e,t,r,i){return g(t,Intl.getCanonicalLocales(e),{localeMatcher:(null==i?void 0:i.algorithm)||"best fit"},[],{},function(){return r}).locale}},5664:(e,t,r)=>{"use strict";var i=r(7897),n=r(7702),a=r(8147),s=r(7873);function o(e){if(!(this instanceof o))return new o(e);this.request=e}e.exports=o,e.exports.Negotiator=o,o.prototype.charset=function(e){var t=this.charsets(e);return t&&t[0]},o.prototype.charsets=function(e){return i(this.request.headers["accept-charset"],e)},o.prototype.encoding=function(e,t){var r=this.encodings(e,t);return r&&r[0]},o.prototype.encodings=function(e,t){return n(this.request.headers["accept-encoding"],e,(t||{}).preferred)},o.prototype.language=function(e){var t=this.languages(e);return t&&t[0]},o.prototype.languages=function(e){return a(this.request.headers["accept-language"],e)},o.prototype.mediaType=function(e){var t=this.mediaTypes(e);return t&&t[0]},o.prototype.mediaTypes=function(e){return s(this.request.headers.accept,e)},o.prototype.preferredCharset=o.prototype.charset,o.prototype.preferredCharsets=o.prototype.charsets,o.prototype.preferredEncoding=o.prototype.encoding,o.prototype.preferredEncodings=o.prototype.encodings,o.prototype.preferredLanguage=o.prototype.language,o.prototype.preferredLanguages=o.prototype.languages,o.prototype.preferredMediaType=o.prototype.mediaType,o.prototype.preferredMediaTypes=o.prototype.mediaTypes},7897:e=>{"use strict";e.exports=r,e.exports.preferredCharsets=r;var t=/^\s*([^\s;]+)\s*(?:;(.*))?$/;function r(e,r){var s=function(e){for(var r=e.split(","),i=0,n=0;i<r.length;i++){var a=function(e,r){var i=t.exec(e);if(!i)return null;var n=i[1],a=1;if(i[2])for(var s=i[2].split(";"),o=0;o<s.length;o++){var l=s[o].trim().split("=");if("q"===l[0]){a=parseFloat(l[1]);break}}return{charset:n,q:a,i:r}}(r[i].trim(),i);a&&(r[n++]=a)}return r.length=n,r}(void 0===e?"*":e||"");if(!r)return s.filter(a).sort(i).map(n);var o=r.map(function(e,t){return function(e,t,r){for(var i={o:-1,q:0,s:0},n=0;n<t.length;n++){var a=function(e,t,r){var i=0;if(t.charset.toLowerCase()===e.toLowerCase())i|=1;else if("*"!==t.charset)return null;return{i:r,o:t.i,q:t.q,s:i}}(e,t[n],r);a&&0>(i.s-a.s||i.q-a.q||i.o-a.o)&&(i=a)}return i}(e,s,t)});return o.filter(a).sort(i).map(function(e){return r[o.indexOf(e)]})}function i(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function n(e){return e.charset}function a(e){return e.q>0}},7702:e=>{"use strict";e.exports=i,e.exports.preferredEncodings=i;var t=/^\s*([^\s;]+)\s*(?:;(.*))?$/;function r(e,t,r){var i=0;if(t.encoding.toLowerCase()===e.toLowerCase())i|=1;else if("*"!==t.encoding)return null;return{encoding:e,i:r,o:t.i,q:t.q,s:i}}function i(e,i,o){var l=function(e){for(var i=e.split(","),n=!1,a=1,s=0,o=0;s<i.length;s++){var l=function(e,r){var i=t.exec(e);if(!i)return null;var n=i[1],a=1;if(i[2])for(var s=i[2].split(";"),o=0;o<s.length;o++){var l=s[o].trim().split("=");if("q"===l[0]){a=parseFloat(l[1]);break}}return{encoding:n,q:a,i:r}}(i[s].trim(),s);l&&(i[o++]=l,n=n||r("identity",l),a=Math.min(a,l.q||1))}return n||(i[o++]={encoding:"identity",q:a,i:s}),i.length=o,i}(e||""),d=o?function(e,t){if(e.q!==t.q)return t.q-e.q;var r=o.indexOf(e.encoding),i=o.indexOf(t.encoding);return -1===r&&-1===i?t.s-e.s||e.o-t.o||e.i-t.i:-1!==r&&-1!==i?r-i:-1===r?1:-1}:n;if(!i)return l.filter(s).sort(d).map(a);var c=i.map(function(e,t){return function(e,t,i){for(var n={encoding:e,o:-1,q:0,s:0},a=0;a<t.length;a++){var s=r(e,t[a],i);s&&0>(n.s-s.s||n.q-s.q||n.o-s.o)&&(n=s)}return n}(e,l,t)});return c.filter(s).sort(d).map(function(e){return i[c.indexOf(e)]})}function n(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i}function a(e){return e.encoding}function s(e){return e.q>0}},8147:e=>{"use strict";e.exports=i,e.exports.preferredLanguages=i;var t=/^\s*([^\s\-;]+)(?:-([^\s;]+))?\s*(?:;(.*))?$/;function r(e,r){var i=t.exec(e);if(!i)return null;var n=i[1],a=i[2],s=n;a&&(s+="-"+a);var o=1;if(i[3])for(var l=i[3].split(";"),d=0;d<l.length;d++){var c=l[d].split("=");"q"===c[0]&&(o=parseFloat(c[1]))}return{prefix:n,suffix:a,q:o,i:r,full:s}}function i(e,t){var i=function(e){for(var t=e.split(","),i=0,n=0;i<t.length;i++){var a=r(t[i].trim(),i);a&&(t[n++]=a)}return t.length=n,t}(void 0===e?"*":e||"");if(!t)return i.filter(s).sort(n).map(a);var o=t.map(function(e,t){return function(e,t,i){for(var n={o:-1,q:0,s:0},a=0;a<t.length;a++){var s=function(e,t,i){var n=r(e);if(!n)return null;var a=0;if(t.full.toLowerCase()===n.full.toLowerCase())a|=4;else if(t.prefix.toLowerCase()===n.full.toLowerCase())a|=2;else if(t.full.toLowerCase()===n.prefix.toLowerCase())a|=1;else if("*"!==t.full)return null;return{i:i,o:t.i,q:t.q,s:a}}(e,t[a],i);s&&0>(n.s-s.s||n.q-s.q||n.o-s.o)&&(n=s)}return n}(e,i,t)});return o.filter(s).sort(n).map(function(e){return t[o.indexOf(e)]})}function n(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function a(e){return e.full}function s(e){return e.q>0}},7873:e=>{"use strict";e.exports=i,e.exports.preferredMediaTypes=i;var t=/^\s*([^\s\/;]+)\/([^;\s]+)\s*(?:;(.*))?$/;function r(e,r){var i=t.exec(e);if(!i)return null;var n=Object.create(null),a=1,s=i[2],d=i[1];if(i[3])for(var c=(function(e){for(var t=e.split(";"),r=1,i=0;r<t.length;r++)o(t[i])%2==0?t[++i]=t[r]:t[i]+=";"+t[r];t.length=i+1;for(var r=0;r<t.length;r++)t[r]=t[r].trim();return t})(i[3]).map(l),u=0;u<c.length;u++){var p=c[u],m=p[0].toLowerCase(),g=p[1],h=g&&'"'===g[0]&&'"'===g[g.length-1]?g.slice(1,-1):g;if("q"===m){a=parseFloat(h);break}n[m]=h}return{type:d,subtype:s,params:n,q:a,i:r}}function i(e,t){var i=function(e){for(var t=function(e){for(var t=e.split(","),r=1,i=0;r<t.length;r++)o(t[i])%2==0?t[++i]=t[r]:t[i]+=","+t[r];return t.length=i+1,t}(e),i=0,n=0;i<t.length;i++){var a=r(t[i].trim(),i);a&&(t[n++]=a)}return t.length=n,t}(void 0===e?"*/*":e||"");if(!t)return i.filter(s).sort(n).map(a);var l=t.map(function(e,t){return function(e,t,i){for(var n={o:-1,q:0,s:0},a=0;a<t.length;a++){var s=function(e,t,i){var n=r(e),a=0;if(!n)return null;if(t.type.toLowerCase()==n.type.toLowerCase())a|=4;else if("*"!=t.type)return null;if(t.subtype.toLowerCase()==n.subtype.toLowerCase())a|=2;else if("*"!=t.subtype)return null;var s=Object.keys(t.params);if(s.length>0){if(!s.every(function(e){return"*"==t.params[e]||(t.params[e]||"").toLowerCase()==(n.params[e]||"").toLowerCase()}))return null;a|=1}return{i:i,o:t.i,q:t.q,s:a}}(e,t[a],i);s&&0>(n.s-s.s||n.q-s.q||n.o-s.o)&&(n=s)}return n}(e,i,t)});return l.filter(s).sort(n).map(function(e){return t[l.indexOf(e)]})}function n(e,t){return t.q-e.q||t.s-e.s||e.o-t.o||e.i-t.i||0}function a(e){return e.type+"/"+e.subtype}function s(e){return e.q>0}function o(e){for(var t=0,r=0;-1!==(r=e.indexOf('"',r));)t++,r++;return t}function l(e){var t,r,i=e.indexOf("=");return -1===i?t=e:(t=e.slice(0,i),r=e.slice(i+1)),[t,r]}},6124:(e,t,r)=>{"use strict";var i=r(5975);t.Z=i.default},623:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(7678),n=r(4406);t.default=function(e){var t;let{localizedPathnames:r,request:a,resolvedLocale:s,routing:o}=e,l=a.nextUrl.clone(),d=n.getHost(a.headers);function c(e,t){return e.pathname=i.normalizeTrailingSlash(e.pathname),a.nextUrl.basePath&&((e=new URL(e)).pathname=n.applyBasePath(e.pathname,a.nextUrl.basePath)),"<".concat(e.toString(),'>; rel="alternate"; hreflang="').concat(t,'"')}function u(e,t){return r&&"object"==typeof r?n.formatTemplatePathname(e,r[s],r[t]):e}d&&(l.port="",l.host=d),l.protocol=null!==(t=a.headers.get("x-forwarded-proto"))&&void 0!==t?t:l.protocol,l.pathname=n.getNormalizedPathname(l.pathname,o.locales,o.localePrefix);let p=n.getLocalePrefixes(o.locales,o.localePrefix,!1).flatMap(e=>{let t,[i,a]=e;function s(e){return"/"===e?a:a+e}if(o.domains)return o.domains.filter(e=>n.isLocaleSupportedOnDomain(i,e)).map(e=>((t=new URL(l)).port="",t.host=e.domain,t.pathname=u(l.pathname,i),i===e.defaultLocale&&"always"!==o.localePrefix.mode||(t.pathname=s(t.pathname)),c(t,i)));{let e;e=r&&"object"==typeof r?u(l.pathname,i):l.pathname,i===o.defaultLocale&&"always"!==o.localePrefix.mode||(e=s(e)),t=new URL(e,l)}return c(t,i)});if(!o.domains&&("always"!==o.localePrefix.mode||"/"===l.pathname)){let e=new URL(u(l.pathname,o.defaultLocale),l);p.push(c(e,"x-default"))}return p.join(", ")}},5975:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(4635),n=r(6136),a=r(3110),s=r(7678),o=r(623),l=r(5752),d=r(9592),c=r(4406);t.default=function(e,t){var r,u,p;let m=n.receiveRoutingConfig({...e,alternateLinks:null!==(r=null==t?void 0:t.alternateLinks)&&void 0!==r?r:e.alternateLinks,localeDetection:null!==(u=null==t?void 0:t.localeDetection)&&void 0!==u?u:e.localeDetection,localeCookie:null!==(p=null==t?void 0:t.localeCookie)&&void 0!==p?p:e.localeCookie});return function(e){var t;let r;try{r=decodeURI(e.nextUrl.pathname)}catch(e){return i.NextResponse.next()}let n=c.sanitizePathname(r),{domain:u,locale:p}=l.default(m,e.headers,e.cookies,n),g=u?u.defaultLocale===p:p===m.defaultLocale,h=(null===(t=m.domains)||void 0===t?void 0:t.filter(e=>c.isLocaleSupportedOnDomain(p,e)))||[],f=null!=m.domains&&!u;function y(t){let r=new URL(t,e.url);e.nextUrl.basePath&&(r.pathname=c.applyBasePath(r.pathname,e.nextUrl.basePath));let n=new Headers(e.headers);return n.set(a.HEADER_LOCALE_NAME,p),i.NextResponse.rewrite(r,{request:{headers:n}})}function _(t,r){var n,a;let o=new URL(t,e.url);if(o.pathname=s.normalizeTrailingSlash(o.pathname),h.length>0&&!r&&u){let e=c.getBestMatchingDomain(u,p,h);e&&(r=e.domain,e.defaultLocale===p&&"as-needed"===m.localePrefix.mode&&(o.pathname=c.getNormalizedPathname(o.pathname,m.locales,m.localePrefix)))}return r&&(o.host=r,e.headers.get("x-forwarded-host")&&(o.protocol=null!==(n=e.headers.get("x-forwarded-proto"))&&void 0!==n?n:e.nextUrl.protocol,o.port=null!==(a=e.headers.get("x-forwarded-port"))&&void 0!==a?a:"")),e.nextUrl.basePath&&(o.pathname=c.applyBasePath(o.pathname,e.nextUrl.basePath)),i.NextResponse.redirect(o.toString())}let v=c.getNormalizedPathname(n,m.locales,m.localePrefix),w=c.getPathnameMatch(n,m.locales,m.localePrefix),b=null!=w,S="never"===m.localePrefix.mode||g&&"as-needed"===m.localePrefix.mode,T,x,P=v,C=m.pathnames;if(C){let t;if([t,x]=c.getInternalTemplate(C,v,p),x){let r=C[x],i="string"==typeof r?r:r[p];if(s.matchesPathname(i,v))P=c.formatTemplatePathname(v,i,x);else{let n;n=t?"string"==typeof r?r:r[t]:x;let a=S?void 0:s.getLocalePrefix(p,m.localePrefix),o=c.formatTemplatePathname(v,n,i);T=_(c.formatPathname(o,a,e.nextUrl.search))}}}if(!T){if("/"!==P||b){let t=c.formatPathname(P,c.getLocaleAsPrefix(p),e.nextUrl.search);if(b){let r=c.formatPathname(v,w.prefix,e.nextUrl.search);if("never"===m.localePrefix.mode)T=_(c.formatPathname(v,void 0,e.nextUrl.search));else if(w.exact){if(g&&S)T=_(c.formatPathname(v,void 0,e.nextUrl.search));else if(m.domains){let e=c.getBestMatchingDomain(u,w.locale,h);T=(null==u?void 0:u.domain)===(null==e?void 0:e.domain)||f?y(t):_(r,null==e?void 0:e.domain)}else T=y(t)}else T=_(r)}else T=S?y(t):_(c.formatPathname(v,s.getLocalePrefix(p,m.localePrefix),e.nextUrl.search))}else T=S?y(c.formatPathname(P,c.getLocaleAsPrefix(p),e.nextUrl.search)):_(c.formatPathname(v,s.getLocalePrefix(p,m.localePrefix),e.nextUrl.search))}return m.localeDetection&&m.localeCookie&&d.default(e,T,p,m.localeCookie),"never"!==m.localePrefix.mode&&m.alternateLinks&&m.locales.length>1&&T.headers.set("Link",o.default({routing:m,localizedPathnames:null!=x&&C?C[x]:void 0,request:e,resolvedLocale:p})),T}}},5752:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(1354),n=r(5664),a=r(4406),s=function(e){return e&&e.__esModule?e:{default:e}}(n);function o(e,t,r){let n;let a=new s.default({headers:{"accept-language":e.get("accept-language")||void 0}}).languages();try{let e=t.slice().sort((e,t)=>t.length-e.length);n=i.match(a,e,r)}catch(e){}return n}function l(e,t){if(e.localeCookie&&t.has(e.localeCookie.name)){var r;let i=null===(r=t.get(e.localeCookie.name))||void 0===r?void 0:r.value;if(i&&e.locales.includes(i))return i}}function d(e,t,r,i){var n;let s;return i&&(s=null===(n=a.getPathnameMatch(i,e.locales,e.localePrefix))||void 0===n?void 0:n.locale),!s&&e.localeDetection&&(s=l(e,r)),!s&&e.localeDetection&&(s=o(t,e.locales,e.defaultLocale)),s||(s=e.defaultLocale),s}t.default=function(e,t,r,i){return e.domains?function(e,t,r,i){let n;let s=function(e,t){let r=a.getHost(e);if(r)return t.find(e=>e.domain===r)}(t,e.domains);if(!s)return{locale:d(e,t,r,i)};if(i){var c;let t=null===(c=a.getPathnameMatch(i,e.locales,e.localePrefix))||void 0===c?void 0:c.locale;if(t){if(!a.isLocaleSupportedOnDomain(t,s))return{locale:t,domain:s};n=t}}if(!n&&e.localeDetection){let t=l(e,r);t&&a.isLocaleSupportedOnDomain(t,s)&&(n=t)}if(!n&&e.localeDetection){let r=o(t,s.locales||e.locales,s.defaultLocale);r&&(n=r)}return n||(n=s.defaultLocale),{locale:n,domain:s}}(e,t,r,i):{locale:d(e,t,r,i)}},t.getAcceptLanguageLocale=o},9592:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t,r,i){var n;let{name:a,...s}=i;(null===(n=e.cookies.get(a))||void 0===n?void 0:n.value)!==r&&t.cookies.set(a,r,{path:e.nextUrl.basePath||void 0,...s})}},4406:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var i=r(7678);function n(e,t){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],n=e.map(e=>[e,i.getLocalePrefix(e,t)]);return r&&n.sort((e,t)=>t[1].length-e[1].length),n}function a(e,t){let r=i.normalizeTrailingSlash(t),n=i.normalizeTrailingSlash(e),a=i.templateToRegex(n).exec(r);if(!a)return;let s={};for(let e=1;e<a.length;e++){var o;let t=null===(o=n.match(/\[([^\]]+)\]/g))||void 0===o?void 0:o[e-1].replace(/[[\]]/g,"");t&&(s[t]=a[e])}return s}function s(e,t){if(!t)return e;let r=e=e.replace(/\[\[/g,"[").replace(/\]\]/g,"]");return Object.entries(t).forEach(e=>{let[t,i]=e;r=r.replace("[".concat(t,"]"),i)}),r}function o(e,t){return t.defaultLocale===e||!t.locales||t.locales.includes(e)}t.applyBasePath=function(e,t){return i.normalizeTrailingSlash(t+e)},t.formatPathname=function(e,t,r){let n=e;return t&&(n=i.prefixPathname(t,n)),r&&(n+=r),n},t.formatPathnameTemplate=s,t.formatTemplatePathname=function(e,t,r,n){let o="";return o+=s(r,a(t,e)),o=i.normalizeTrailingSlash(o)},t.getBestMatchingDomain=function(e,t,r){let i;return e&&o(t,e)&&(i=e),i||(i=r.find(e=>e.defaultLocale===t)),i||(i=r.find(e=>{var r;return null===(r=e.locales)||void 0===r?void 0:r.includes(t)})),i||null!=(null==e?void 0:e.locales)||(i=e),i||(i=r.find(e=>!e.locales)),i},t.getHost=function(e){var t,r;return null!==(t=null!==(r=e.get("x-forwarded-host"))&&void 0!==r?r:e.get("host"))&&void 0!==t?t:void 0},t.getInternalTemplate=function(e,t,r){for(let n of i.getSortedPathnames(Object.keys(e))){let a=e[n];if("string"==typeof a){if(i.matchesPathname(a,t))return[void 0,n]}else{let e=Object.entries(a),s=e.findIndex(e=>{let[t]=e;return t===r});for(let[r,a]of(s>0&&e.unshift(e.splice(s,1)[0]),e))if(i.matchesPathname(a,t))return[r,n]}}for(let r of Object.keys(e))if(i.matchesPathname(r,t))return[void 0,r];return[void 0,void 0]},t.getLocaleAsPrefix=function(e){return"/".concat(e)},t.getLocalePrefixes=n,t.getNormalizedPathname=function(e,t,r){e.endsWith("/")||(e+="/");let a=n(t,r),s=RegExp("^(".concat(a.map(e=>{let[,t]=e;return t.replaceAll("/","\\/")}).join("|"),")/(.*)"),"i"),o=e.match(s),l=o?"/"+o[2]:e;return"/"!==l&&(l=i.normalizeTrailingSlash(l)),l},t.getPathnameMatch=function(e,t,r){for(let[i,a]of n(t,r)){let t,r;if(e===a||e.startsWith(a+"/"))t=r=!0;else{let i=e.toLowerCase(),n=a.toLowerCase();(i===n||i.startsWith(n+"/"))&&(t=!1,r=!0)}if(r)return{locale:i,prefix:a,matchedPrefix:e.slice(0,a.length),exact:t}}},t.getRouteParams=a,t.isLocaleSupportedOnDomain=o,t.sanitizePathname=function(e){return e.replace(/\\/g,"%5C").replace(/\/+/g,"/")}},6136:(e,t)=>{"use strict";function r(e){return!(null!=e&&!e)&&{name:"NEXT_LOCALE",maxAge:31536e3,sameSite:"lax",..."object"==typeof e&&e}}function i(e){return"object"==typeof e?e:{mode:e||"always"}}Object.defineProperty(t,"__esModule",{value:!0}),t.receiveLocaleCookie=r,t.receiveLocalePrefixConfig=i,t.receiveRoutingConfig=function(e){var t,n;return{...e,localePrefix:i(e.localePrefix),localeCookie:r(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(n=e.alternateLinks)||void 0===n||n}}},3142:(e,t,r)=>{"use strict";var i=r(8025);i.getFormatter,i.getLocale,i.getMessages,i.getNow,t.cF=i.getRequestConfig,i.getTimeZone,i.getTranslations,i.setRequestLocale,i.unstable_setRequestLocale},8025:(e,t)=>{"use strict";function r(e){return()=>{throw Error("`".concat(e,"` is not supported in Client Components."))}}Object.defineProperty(t,"__esModule",{value:!0});let i=r("getFormatter"),n=r("getNow"),a=r("getTimeZone"),s=r("getMessages"),o=r("getLocale"),l=r("getTranslations"),d=r("unstable_setRequestLocale"),c=r("setRequestLocale");t.getFormatter=i,t.getLocale=o,t.getMessages=s,t.getNow=n,t.getRequestConfig=function(){return r("getRequestConfig")},t.getTimeZone=a,t.getTranslations=l,t.setRequestLocale=c,t.unstable_setRequestLocale=d},3110:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},7678:(e,t)=>{"use strict";function r(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){let r;return"string"==typeof e?r=n(t,e):(r={...e},e.pathname&&(r.pathname=n(t,e.pathname))),r}function n(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}function a(e,t){return t===e||t.startsWith("".concat(e,"/"))}function s(e){let t=function(){try{return"true"===process.env._next_intl_trailing_slash}catch(e){return!1}}();if("/"!==e){let r=e.endsWith("/");t&&!r?e+="/":!t&&r&&(e=e.slice(0,-1))}return e}function o(e){return"/"+e}function l(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return new RegExp("^".concat(t,"$"))}function d(e){return e.includes("[[...")}function c(e){return e.includes("[...")}function u(e){return e.includes("[")}function p(e,t){let r=e.split("/"),i=t.split("/"),n=Math.max(r.length,i.length);for(let e=0;e<n;e++){let t=r[e],n=i[e];if(!t&&n)return -1;if(t&&!n)return 1;if(t||n){if(!u(t)&&u(n))return -1;if(u(t)&&!u(n))return 1;if(!c(t)&&c(n))return -1;if(c(t)&&!c(n))return 1;if(!d(t)&&d(n))return -1;if(d(t)&&!d(n))return 1}}return 0}Object.defineProperty(t,"__esModule",{value:!0}),t.getLocaleAsPrefix=o,t.getLocalePrefix=function(e,t){var r;return"never"!==t.mode&&(null===(r=t.prefixes)||void 0===r?void 0:r[e])||o(e)},t.getSortedPathnames=function(e){return e.sort(p)},t.hasPathnamePrefixed=a,t.isLocalizableHref=r,t.isPromise=function(e){return"function"==typeof e.then},t.localizeHref=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,s=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!r(e))return e;let l=a(o,s);return(t!==n||l)&&null!=o?i(e,o):e},t.matchesPathname=function(e,t){let r=s(e),i=s(t);return l(r).test(i)},t.normalizeTrailingSlash=s,t.prefixHref=i,t.prefixPathname=n,t.templateToRegex=l,t.unprefixPathname=function(e,t){return e.replace(new RegExp("^".concat(t)),"")||"/"}},5945:e=>{"use strict";var t=Object.defineProperty,r=Object.getOwnPropertyDescriptor,i=Object.getOwnPropertyNames,n=Object.prototype.hasOwnProperty,a={};function s(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),i=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?i:`${i}; ${r.join("; ")}`}function o(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[i,n]=[r.slice(0,e),r.slice(e+1)];try{t.set(i,decodeURIComponent(null!=n?n:"true"))}catch{}}return t}function l(e){var t,r;if(!e)return;let[[i,n],...a]=o(e),{domain:s,expires:l,httponly:u,maxage:p,path:m,samesite:g,secure:h,partitioned:f,priority:y}=Object.fromEntries(a.map(([e,t])=>[e.toLowerCase(),t]));return function(e){let t={};for(let r in e)e[r]&&(t[r]=e[r]);return t}({name:i,value:decodeURIComponent(n),domain:s,...l&&{expires:new Date(l)},...u&&{httpOnly:!0},..."string"==typeof p&&{maxAge:Number(p)},path:m,...g&&{sameSite:d.includes(t=(t=g).toLowerCase())?t:void 0},...h&&{secure:!0},...y&&{priority:c.includes(r=(r=y).toLowerCase())?r:void 0},...f&&{partitioned:!0}})}((e,r)=>{for(var i in r)t(e,i,{get:r[i],enumerable:!0})})(a,{RequestCookies:()=>u,ResponseCookies:()=>p,parseCookie:()=>o,parseSetCookie:()=>l,stringifyCookie:()=>s}),e.exports=((e,a,s,o)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let l of i(a))n.call(e,l)||l===s||t(e,l,{get:()=>a[l],enumerable:!(o=r(a,l))||o.enumerable});return e})(t({},"__esModule",{value:!0}),a);var d=["strict","lax","none"],c=["low","medium","high"],u=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of o(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===i).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,i=this._parsed;return i.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(i).map(([e,t])=>s(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>s(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}},p=class{constructor(e){var t,r,i;this._parsed=new Map,this._headers=e;let n=null!=(i=null!=(r=null==(t=e.getSetCookie)?void 0:t.call(e))?r:e.get("set-cookie"))?i:[];for(let e of Array.isArray(n)?n:function(e){if(!e)return[];var t,r,i,n,a,s=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=n,s.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}(n)){let t=l(e);t&&this._parsed.set(t.name,t)}}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed.values());if(!e.length)return r;let i="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(e=>e.name===i)}has(e){return this._parsed.has(e)}set(...e){let[t,r,i]=1===e.length?[e[0].name,e[0].value,e[0]]:e,n=this._parsed;return n.set(t,function(e={name:"",value:""}){return"number"==typeof e.expires&&(e.expires=new Date(e.expires)),e.maxAge&&(e.expires=new Date(Date.now()+1e3*e.maxAge)),(null===e.path||void 0===e.path)&&(e.path="/"),e}({name:t,value:r,...i})),function(e,t){for(let[,r]of(t.delete("set-cookie"),e)){let e=s(r);t.append("set-cookie",e)}}(n,this._headers),this}delete(...e){let[t,r,i]="string"==typeof e[0]?[e[0]]:[e[0].name,e[0].path,e[0].domain];return this.set({name:t,path:r,domain:i,value:"",expires:new Date(0)})}[Symbol.for("edge-runtime.inspect.custom")](){return`ResponseCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(s).join("; ")}}},8439:(e,t,r)=>{(()=>{"use strict";var t={491:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ContextAPI=void 0;let i=r(223),n=r(172),a=r(930),s="context",o=new i.NoopContextManager;class l{constructor(){}static getInstance(){return this._instance||(this._instance=new l),this._instance}setGlobalContextManager(e){return(0,n.registerGlobal)(s,e,a.DiagAPI.instance())}active(){return this._getContextManager().active()}with(e,t,r,...i){return this._getContextManager().with(e,t,r,...i)}bind(e,t){return this._getContextManager().bind(e,t)}_getContextManager(){return(0,n.getGlobal)(s)||o}disable(){this._getContextManager().disable(),(0,n.unregisterGlobal)(s,a.DiagAPI.instance())}}t.ContextAPI=l},930:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagAPI=void 0;let i=r(56),n=r(912),a=r(957),s=r(172);class o{constructor(){function e(e){return function(...t){let r=(0,s.getGlobal)("diag");if(r)return r[e](...t)}}let t=this;t.setLogger=(e,r={logLevel:a.DiagLogLevel.INFO})=>{var i,o,l;if(e===t){let e=Error("Cannot use diag as the logger for itself. Please use a DiagLogger implementation like ConsoleDiagLogger or a custom implementation");return t.error(null!==(i=e.stack)&&void 0!==i?i:e.message),!1}"number"==typeof r&&(r={logLevel:r});let d=(0,s.getGlobal)("diag"),c=(0,n.createLogLevelDiagLogger)(null!==(o=r.logLevel)&&void 0!==o?o:a.DiagLogLevel.INFO,e);if(d&&!r.suppressOverrideMessage){let e=null!==(l=Error().stack)&&void 0!==l?l:"<failed to generate stacktrace>";d.warn(`Current logger will be overwritten from ${e}`),c.warn(`Current logger will overwrite one already registered from ${e}`)}return(0,s.registerGlobal)("diag",c,t,!0)},t.disable=()=>{(0,s.unregisterGlobal)("diag",t)},t.createComponentLogger=e=>new i.DiagComponentLogger(e),t.verbose=e("verbose"),t.debug=e("debug"),t.info=e("info"),t.warn=e("warn"),t.error=e("error")}static instance(){return this._instance||(this._instance=new o),this._instance}}t.DiagAPI=o},653:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.MetricsAPI=void 0;let i=r(660),n=r(172),a=r(930),s="metrics";class o{constructor(){}static getInstance(){return this._instance||(this._instance=new o),this._instance}setGlobalMeterProvider(e){return(0,n.registerGlobal)(s,e,a.DiagAPI.instance())}getMeterProvider(){return(0,n.getGlobal)(s)||i.NOOP_METER_PROVIDER}getMeter(e,t,r){return this.getMeterProvider().getMeter(e,t,r)}disable(){(0,n.unregisterGlobal)(s,a.DiagAPI.instance())}}t.MetricsAPI=o},181:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PropagationAPI=void 0;let i=r(172),n=r(874),a=r(194),s=r(277),o=r(369),l=r(930),d="propagation",c=new n.NoopTextMapPropagator;class u{constructor(){this.createBaggage=o.createBaggage,this.getBaggage=s.getBaggage,this.getActiveBaggage=s.getActiveBaggage,this.setBaggage=s.setBaggage,this.deleteBaggage=s.deleteBaggage}static getInstance(){return this._instance||(this._instance=new u),this._instance}setGlobalPropagator(e){return(0,i.registerGlobal)(d,e,l.DiagAPI.instance())}inject(e,t,r=a.defaultTextMapSetter){return this._getGlobalPropagator().inject(e,t,r)}extract(e,t,r=a.defaultTextMapGetter){return this._getGlobalPropagator().extract(e,t,r)}fields(){return this._getGlobalPropagator().fields()}disable(){(0,i.unregisterGlobal)(d,l.DiagAPI.instance())}_getGlobalPropagator(){return(0,i.getGlobal)(d)||c}}t.PropagationAPI=u},997:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceAPI=void 0;let i=r(172),n=r(846),a=r(139),s=r(607),o=r(930),l="trace";class d{constructor(){this._proxyTracerProvider=new n.ProxyTracerProvider,this.wrapSpanContext=a.wrapSpanContext,this.isSpanContextValid=a.isSpanContextValid,this.deleteSpan=s.deleteSpan,this.getSpan=s.getSpan,this.getActiveSpan=s.getActiveSpan,this.getSpanContext=s.getSpanContext,this.setSpan=s.setSpan,this.setSpanContext=s.setSpanContext}static getInstance(){return this._instance||(this._instance=new d),this._instance}setGlobalTracerProvider(e){let t=(0,i.registerGlobal)(l,this._proxyTracerProvider,o.DiagAPI.instance());return t&&this._proxyTracerProvider.setDelegate(e),t}getTracerProvider(){return(0,i.getGlobal)(l)||this._proxyTracerProvider}getTracer(e,t){return this.getTracerProvider().getTracer(e,t)}disable(){(0,i.unregisterGlobal)(l,o.DiagAPI.instance()),this._proxyTracerProvider=new n.ProxyTracerProvider}}t.TraceAPI=d},277:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deleteBaggage=t.setBaggage=t.getActiveBaggage=t.getBaggage=void 0;let i=r(491),n=(0,r(780).createContextKey)("OpenTelemetry Baggage Key");function a(e){return e.getValue(n)||void 0}t.getBaggage=a,t.getActiveBaggage=function(){return a(i.ContextAPI.getInstance().active())},t.setBaggage=function(e,t){return e.setValue(n,t)},t.deleteBaggage=function(e){return e.deleteValue(n)}},993:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaggageImpl=void 0;class r{constructor(e){this._entries=e?new Map(e):new Map}getEntry(e){let t=this._entries.get(e);if(t)return Object.assign({},t)}getAllEntries(){return Array.from(this._entries.entries()).map(([e,t])=>[e,t])}setEntry(e,t){let i=new r(this._entries);return i._entries.set(e,t),i}removeEntry(e){let t=new r(this._entries);return t._entries.delete(e),t}removeEntries(...e){let t=new r(this._entries);for(let r of e)t._entries.delete(r);return t}clear(){return new r}}t.BaggageImpl=r},830:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataSymbol=void 0,t.baggageEntryMetadataSymbol=Symbol("BaggageEntryMetadata")},369:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.baggageEntryMetadataFromString=t.createBaggage=void 0;let i=r(930),n=r(993),a=r(830),s=i.DiagAPI.instance();t.createBaggage=function(e={}){return new n.BaggageImpl(new Map(Object.entries(e)))},t.baggageEntryMetadataFromString=function(e){return"string"!=typeof e&&(s.error(`Cannot create baggage metadata from unknown type: ${typeof e}`),e=""),{__TYPE__:a.baggageEntryMetadataSymbol,toString:()=>e}}},67:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.context=void 0;let i=r(491);t.context=i.ContextAPI.getInstance()},223:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopContextManager=void 0;let i=r(780);class n{active(){return i.ROOT_CONTEXT}with(e,t,r,...i){return t.call(r,...i)}bind(e,t){return t}enable(){return this}disable(){return this}}t.NoopContextManager=n},780:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_CONTEXT=t.createContextKey=void 0,t.createContextKey=function(e){return Symbol.for(e)};class r{constructor(e){let t=this;t._currentContext=e?new Map(e):new Map,t.getValue=e=>t._currentContext.get(e),t.setValue=(e,i)=>{let n=new r(t._currentContext);return n._currentContext.set(e,i),n},t.deleteValue=e=>{let i=new r(t._currentContext);return i._currentContext.delete(e),i}}}t.ROOT_CONTEXT=new r},506:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.diag=void 0;let i=r(930);t.diag=i.DiagAPI.instance()},56:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagComponentLogger=void 0;let i=r(172);class n{constructor(e){this._namespace=e.namespace||"DiagComponentLogger"}debug(...e){return a("debug",this._namespace,e)}error(...e){return a("error",this._namespace,e)}info(...e){return a("info",this._namespace,e)}warn(...e){return a("warn",this._namespace,e)}verbose(...e){return a("verbose",this._namespace,e)}}function a(e,t,r){let n=(0,i.getGlobal)("diag");if(n)return r.unshift(t),n[e](...r)}t.DiagComponentLogger=n},972:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagConsoleLogger=void 0;let r=[{n:"error",c:"error"},{n:"warn",c:"warn"},{n:"info",c:"info"},{n:"debug",c:"debug"},{n:"verbose",c:"trace"}];class i{constructor(){for(let e=0;e<r.length;e++)this[r[e].n]=function(e){return function(...t){if(console){let r=console[e];if("function"!=typeof r&&(r=console.log),"function"==typeof r)return r.apply(console,t)}}}(r[e].c)}}t.DiagConsoleLogger=i},912:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createLogLevelDiagLogger=void 0;let i=r(957);t.createLogLevelDiagLogger=function(e,t){function r(r,i){let n=t[r];return"function"==typeof n&&e>=i?n.bind(t):function(){}}return e<i.DiagLogLevel.NONE?e=i.DiagLogLevel.NONE:e>i.DiagLogLevel.ALL&&(e=i.DiagLogLevel.ALL),t=t||{},{error:r("error",i.DiagLogLevel.ERROR),warn:r("warn",i.DiagLogLevel.WARN),info:r("info",i.DiagLogLevel.INFO),debug:r("debug",i.DiagLogLevel.DEBUG),verbose:r("verbose",i.DiagLogLevel.VERBOSE)}}},957:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DiagLogLevel=void 0,function(e){e[e.NONE=0]="NONE",e[e.ERROR=30]="ERROR",e[e.WARN=50]="WARN",e[e.INFO=60]="INFO",e[e.DEBUG=70]="DEBUG",e[e.VERBOSE=80]="VERBOSE",e[e.ALL=9999]="ALL"}(t.DiagLogLevel||(t.DiagLogLevel={}))},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.unregisterGlobal=t.getGlobal=t.registerGlobal=void 0;let i=r(200),n=r(521),a=r(130),s=n.VERSION.split(".")[0],o=Symbol.for(`opentelemetry.js.api.${s}`),l=i._globalThis;t.registerGlobal=function(e,t,r,i=!1){var a;let s=l[o]=null!==(a=l[o])&&void 0!==a?a:{version:n.VERSION};if(!i&&s[e]){let t=Error(`@opentelemetry/api: Attempted duplicate registration of API: ${e}`);return r.error(t.stack||t.message),!1}if(s.version!==n.VERSION){let t=Error(`@opentelemetry/api: Registration of version v${s.version} for ${e} does not match previously registered API v${n.VERSION}`);return r.error(t.stack||t.message),!1}return s[e]=t,r.debug(`@opentelemetry/api: Registered a global for ${e} v${n.VERSION}.`),!0},t.getGlobal=function(e){var t,r;let i=null===(t=l[o])||void 0===t?void 0:t.version;if(i&&(0,a.isCompatible)(i))return null===(r=l[o])||void 0===r?void 0:r[e]},t.unregisterGlobal=function(e,t){t.debug(`@opentelemetry/api: Unregistering a global for ${e} v${n.VERSION}.`);let r=l[o];r&&delete r[e]}},130:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isCompatible=t._makeCompatibilityCheck=void 0;let i=r(521),n=/^(\d+)\.(\d+)\.(\d+)(-(.+))?$/;function a(e){let t=new Set([e]),r=new Set,i=e.match(n);if(!i)return()=>!1;let a={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};if(null!=a.prerelease)return function(t){return t===e};function s(e){return r.add(e),!1}return function(e){if(t.has(e))return!0;if(r.has(e))return!1;let i=e.match(n);if(!i)return s(e);let o={major:+i[1],minor:+i[2],patch:+i[3],prerelease:i[4]};return null!=o.prerelease||a.major!==o.major?s(e):0===a.major?a.minor===o.minor&&a.patch<=o.patch?(t.add(e),!0):s(e):a.minor<=o.minor?(t.add(e),!0):s(e)}}t._makeCompatibilityCheck=a,t.isCompatible=a(i.VERSION)},886:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.metrics=void 0;let i=r(653);t.metrics=i.MetricsAPI.getInstance()},901:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ValueType=void 0,function(e){e[e.INT=0]="INT",e[e.DOUBLE=1]="DOUBLE"}(t.ValueType||(t.ValueType={}))},102:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createNoopMeter=t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=t.NOOP_OBSERVABLE_GAUGE_METRIC=t.NOOP_OBSERVABLE_COUNTER_METRIC=t.NOOP_UP_DOWN_COUNTER_METRIC=t.NOOP_HISTOGRAM_METRIC=t.NOOP_COUNTER_METRIC=t.NOOP_METER=t.NoopObservableUpDownCounterMetric=t.NoopObservableGaugeMetric=t.NoopObservableCounterMetric=t.NoopObservableMetric=t.NoopHistogramMetric=t.NoopUpDownCounterMetric=t.NoopCounterMetric=t.NoopMetric=t.NoopMeter=void 0;class r{constructor(){}createHistogram(e,r){return t.NOOP_HISTOGRAM_METRIC}createCounter(e,r){return t.NOOP_COUNTER_METRIC}createUpDownCounter(e,r){return t.NOOP_UP_DOWN_COUNTER_METRIC}createObservableGauge(e,r){return t.NOOP_OBSERVABLE_GAUGE_METRIC}createObservableCounter(e,r){return t.NOOP_OBSERVABLE_COUNTER_METRIC}createObservableUpDownCounter(e,r){return t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC}addBatchObservableCallback(e,t){}removeBatchObservableCallback(e){}}t.NoopMeter=r;class i{}t.NoopMetric=i;class n extends i{add(e,t){}}t.NoopCounterMetric=n;class a extends i{add(e,t){}}t.NoopUpDownCounterMetric=a;class s extends i{record(e,t){}}t.NoopHistogramMetric=s;class o{addCallback(e){}removeCallback(e){}}t.NoopObservableMetric=o;class l extends o{}t.NoopObservableCounterMetric=l;class d extends o{}t.NoopObservableGaugeMetric=d;class c extends o{}t.NoopObservableUpDownCounterMetric=c,t.NOOP_METER=new r,t.NOOP_COUNTER_METRIC=new n,t.NOOP_HISTOGRAM_METRIC=new s,t.NOOP_UP_DOWN_COUNTER_METRIC=new a,t.NOOP_OBSERVABLE_COUNTER_METRIC=new l,t.NOOP_OBSERVABLE_GAUGE_METRIC=new d,t.NOOP_OBSERVABLE_UP_DOWN_COUNTER_METRIC=new c,t.createNoopMeter=function(){return t.NOOP_METER}},660:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NOOP_METER_PROVIDER=t.NoopMeterProvider=void 0;let i=r(102);class n{getMeter(e,t,r){return i.NOOP_METER}}t.NoopMeterProvider=n,t.NOOP_METER_PROVIDER=new n},200:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(46),t)},651:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t._globalThis=void 0,t._globalThis="object"==typeof globalThis?globalThis:r.g},46:function(e,t,r){var i=this&&this.__createBinding||(Object.create?function(e,t,r,i){void 0===i&&(i=r),Object.defineProperty(e,i,{enumerable:!0,get:function(){return t[r]}})}:function(e,t,r,i){void 0===i&&(i=r),e[i]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||i(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),n(r(651),t)},939:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.propagation=void 0;let i=r(181);t.propagation=i.PropagationAPI.getInstance()},874:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTextMapPropagator=void 0;class r{inject(e,t){}extract(e,t){return e}fields(){return[]}}t.NoopTextMapPropagator=r},194:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.defaultTextMapSetter=t.defaultTextMapGetter=void 0,t.defaultTextMapGetter={get(e,t){if(null!=e)return e[t]},keys:e=>null==e?[]:Object.keys(e)},t.defaultTextMapSetter={set(e,t,r){null!=e&&(e[t]=r)}}},845:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.trace=void 0;let i=r(997);t.trace=i.TraceAPI.getInstance()},403:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NonRecordingSpan=void 0;let i=r(476);class n{constructor(e=i.INVALID_SPAN_CONTEXT){this._spanContext=e}spanContext(){return this._spanContext}setAttribute(e,t){return this}setAttributes(e){return this}addEvent(e,t){return this}setStatus(e){return this}updateName(e){return this}end(e){}isRecording(){return!1}recordException(e,t){}}t.NonRecordingSpan=n},614:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracer=void 0;let i=r(491),n=r(607),a=r(403),s=r(139),o=i.ContextAPI.getInstance();class l{startSpan(e,t,r=o.active()){if(null==t?void 0:t.root)return new a.NonRecordingSpan;let i=r&&(0,n.getSpanContext)(r);return"object"==typeof i&&"string"==typeof i.spanId&&"string"==typeof i.traceId&&"number"==typeof i.traceFlags&&(0,s.isSpanContextValid)(i)?new a.NonRecordingSpan(i):new a.NonRecordingSpan}startActiveSpan(e,t,r,i){let a,s,l;if(arguments.length<2)return;2==arguments.length?l=t:3==arguments.length?(a=t,l=r):(a=t,s=r,l=i);let d=null!=s?s:o.active(),c=this.startSpan(e,a,d),u=(0,n.setSpan)(d,c);return o.with(u,l,void 0,c)}}t.NoopTracer=l},124:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NoopTracerProvider=void 0;let i=r(614);class n{getTracer(e,t,r){return new i.NoopTracer}}t.NoopTracerProvider=n},125:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracer=void 0;let i=new(r(614)).NoopTracer;class n{constructor(e,t,r,i){this._provider=e,this.name=t,this.version=r,this.options=i}startSpan(e,t,r){return this._getTracer().startSpan(e,t,r)}startActiveSpan(e,t,r,i){let n=this._getTracer();return Reflect.apply(n.startActiveSpan,n,arguments)}_getTracer(){if(this._delegate)return this._delegate;let e=this._provider.getDelegateTracer(this.name,this.version,this.options);return e?(this._delegate=e,this._delegate):i}}t.ProxyTracer=n},846:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ProxyTracerProvider=void 0;let i=r(125),n=new(r(124)).NoopTracerProvider;class a{getTracer(e,t,r){var n;return null!==(n=this.getDelegateTracer(e,t,r))&&void 0!==n?n:new i.ProxyTracer(this,e,t,r)}getDelegate(){var e;return null!==(e=this._delegate)&&void 0!==e?e:n}setDelegate(e){this._delegate=e}getDelegateTracer(e,t,r){var i;return null===(i=this._delegate)||void 0===i?void 0:i.getTracer(e,t,r)}}t.ProxyTracerProvider=a},996:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SamplingDecision=void 0,function(e){e[e.NOT_RECORD=0]="NOT_RECORD",e[e.RECORD=1]="RECORD",e[e.RECORD_AND_SAMPLED=2]="RECORD_AND_SAMPLED"}(t.SamplingDecision||(t.SamplingDecision={}))},607:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getSpanContext=t.setSpanContext=t.deleteSpan=t.setSpan=t.getActiveSpan=t.getSpan=void 0;let i=r(780),n=r(403),a=r(491),s=(0,i.createContextKey)("OpenTelemetry Context Key SPAN");function o(e){return e.getValue(s)||void 0}function l(e,t){return e.setValue(s,t)}t.getSpan=o,t.getActiveSpan=function(){return o(a.ContextAPI.getInstance().active())},t.setSpan=l,t.deleteSpan=function(e){return e.deleteValue(s)},t.setSpanContext=function(e,t){return l(e,new n.NonRecordingSpan(t))},t.getSpanContext=function(e){var t;return null===(t=o(e))||void 0===t?void 0:t.spanContext()}},325:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceStateImpl=void 0;let i=r(564);class n{constructor(e){this._internalState=new Map,e&&this._parse(e)}set(e,t){let r=this._clone();return r._internalState.has(e)&&r._internalState.delete(e),r._internalState.set(e,t),r}unset(e){let t=this._clone();return t._internalState.delete(e),t}get(e){return this._internalState.get(e)}serialize(){return this._keys().reduce((e,t)=>(e.push(t+"="+this.get(t)),e),[]).join(",")}_parse(e){!(e.length>512)&&(this._internalState=e.split(",").reverse().reduce((e,t)=>{let r=t.trim(),n=r.indexOf("=");if(-1!==n){let a=r.slice(0,n),s=r.slice(n+1,t.length);(0,i.validateKey)(a)&&(0,i.validateValue)(s)&&e.set(a,s)}return e},new Map),this._internalState.size>32&&(this._internalState=new Map(Array.from(this._internalState.entries()).reverse().slice(0,32))))}_keys(){return Array.from(this._internalState.keys()).reverse()}_clone(){let e=new n;return e._internalState=new Map(this._internalState),e}}t.TraceStateImpl=n},564:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.validateValue=t.validateKey=void 0;let r="[_0-9a-z-*/]",i=`[a-z]${r}{0,255}`,n=`[a-z0-9]${r}{0,240}@[a-z]${r}{0,13}`,a=RegExp(`^(?:${i}|${n})$`),s=/^[ -~]{0,255}[!-~]$/,o=/,|=/;t.validateKey=function(e){return a.test(e)},t.validateValue=function(e){return s.test(e)&&!o.test(e)}},98:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.createTraceState=void 0;let i=r(325);t.createTraceState=function(e){return new i.TraceStateImpl(e)}},476:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.INVALID_SPAN_CONTEXT=t.INVALID_TRACEID=t.INVALID_SPANID=void 0;let i=r(475);t.INVALID_SPANID="0000000000000000",t.INVALID_TRACEID="00000000000000000000000000000000",t.INVALID_SPAN_CONTEXT={traceId:t.INVALID_TRACEID,spanId:t.INVALID_SPANID,traceFlags:i.TraceFlags.NONE}},357:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanKind=void 0,function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"}(t.SpanKind||(t.SpanKind={}))},139:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.wrapSpanContext=t.isSpanContextValid=t.isValidSpanId=t.isValidTraceId=void 0;let i=r(476),n=r(403),a=/^([0-9a-f]{32})$/i,s=/^[0-9a-f]{16}$/i;function o(e){return a.test(e)&&e!==i.INVALID_TRACEID}function l(e){return s.test(e)&&e!==i.INVALID_SPANID}t.isValidTraceId=o,t.isValidSpanId=l,t.isSpanContextValid=function(e){return o(e.traceId)&&l(e.spanId)},t.wrapSpanContext=function(e){return new n.NonRecordingSpan(e)}},847:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SpanStatusCode=void 0,function(e){e[e.UNSET=0]="UNSET",e[e.OK=1]="OK",e[e.ERROR=2]="ERROR"}(t.SpanStatusCode||(t.SpanStatusCode={}))},475:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TraceFlags=void 0,function(e){e[e.NONE=0]="NONE",e[e.SAMPLED=1]="SAMPLED"}(t.TraceFlags||(t.TraceFlags={}))},521:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.VERSION=void 0,t.VERSION="1.6.0"}},i={};function n(e){var r=i[e];if(void 0!==r)return r.exports;var a=i[e]={exports:{}},s=!0;try{t[e].call(a.exports,a,a.exports,n),s=!1}finally{s&&delete i[e]}return a.exports}n.ab="//";var a={};(()=>{Object.defineProperty(a,"__esModule",{value:!0}),a.trace=a.propagation=a.metrics=a.diag=a.context=a.INVALID_SPAN_CONTEXT=a.INVALID_TRACEID=a.INVALID_SPANID=a.isValidSpanId=a.isValidTraceId=a.isSpanContextValid=a.createTraceState=a.TraceFlags=a.SpanStatusCode=a.SpanKind=a.SamplingDecision=a.ProxyTracerProvider=a.ProxyTracer=a.defaultTextMapSetter=a.defaultTextMapGetter=a.ValueType=a.createNoopMeter=a.DiagLogLevel=a.DiagConsoleLogger=a.ROOT_CONTEXT=a.createContextKey=a.baggageEntryMetadataFromString=void 0;var e=n(369);Object.defineProperty(a,"baggageEntryMetadataFromString",{enumerable:!0,get:function(){return e.baggageEntryMetadataFromString}});var t=n(780);Object.defineProperty(a,"createContextKey",{enumerable:!0,get:function(){return t.createContextKey}}),Object.defineProperty(a,"ROOT_CONTEXT",{enumerable:!0,get:function(){return t.ROOT_CONTEXT}});var r=n(972);Object.defineProperty(a,"DiagConsoleLogger",{enumerable:!0,get:function(){return r.DiagConsoleLogger}});var i=n(957);Object.defineProperty(a,"DiagLogLevel",{enumerable:!0,get:function(){return i.DiagLogLevel}});var s=n(102);Object.defineProperty(a,"createNoopMeter",{enumerable:!0,get:function(){return s.createNoopMeter}});var o=n(901);Object.defineProperty(a,"ValueType",{enumerable:!0,get:function(){return o.ValueType}});var l=n(194);Object.defineProperty(a,"defaultTextMapGetter",{enumerable:!0,get:function(){return l.defaultTextMapGetter}}),Object.defineProperty(a,"defaultTextMapSetter",{enumerable:!0,get:function(){return l.defaultTextMapSetter}});var d=n(125);Object.defineProperty(a,"ProxyTracer",{enumerable:!0,get:function(){return d.ProxyTracer}});var c=n(846);Object.defineProperty(a,"ProxyTracerProvider",{enumerable:!0,get:function(){return c.ProxyTracerProvider}});var u=n(996);Object.defineProperty(a,"SamplingDecision",{enumerable:!0,get:function(){return u.SamplingDecision}});var p=n(357);Object.defineProperty(a,"SpanKind",{enumerable:!0,get:function(){return p.SpanKind}});var m=n(847);Object.defineProperty(a,"SpanStatusCode",{enumerable:!0,get:function(){return m.SpanStatusCode}});var g=n(475);Object.defineProperty(a,"TraceFlags",{enumerable:!0,get:function(){return g.TraceFlags}});var h=n(98);Object.defineProperty(a,"createTraceState",{enumerable:!0,get:function(){return h.createTraceState}});var f=n(139);Object.defineProperty(a,"isSpanContextValid",{enumerable:!0,get:function(){return f.isSpanContextValid}}),Object.defineProperty(a,"isValidTraceId",{enumerable:!0,get:function(){return f.isValidTraceId}}),Object.defineProperty(a,"isValidSpanId",{enumerable:!0,get:function(){return f.isValidSpanId}});var y=n(476);Object.defineProperty(a,"INVALID_SPANID",{enumerable:!0,get:function(){return y.INVALID_SPANID}}),Object.defineProperty(a,"INVALID_TRACEID",{enumerable:!0,get:function(){return y.INVALID_TRACEID}}),Object.defineProperty(a,"INVALID_SPAN_CONTEXT",{enumerable:!0,get:function(){return y.INVALID_SPAN_CONTEXT}});let _=n(67);Object.defineProperty(a,"context",{enumerable:!0,get:function(){return _.context}});let v=n(506);Object.defineProperty(a,"diag",{enumerable:!0,get:function(){return v.diag}});let w=n(886);Object.defineProperty(a,"metrics",{enumerable:!0,get:function(){return w.metrics}});let b=n(939);Object.defineProperty(a,"propagation",{enumerable:!0,get:function(){return b.propagation}});let S=n(845);Object.defineProperty(a,"trace",{enumerable:!0,get:function(){return S.trace}}),a.default={context:_.context,diag:v.diag,metrics:w.metrics,propagation:b.propagation,trace:S.trace}})(),e.exports=a})()},1133:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var n={},a=t.split(i),s=(r||{}).decode||e,o=0;o<a.length;o++){var l=a[o],d=l.indexOf("=");if(!(d<0)){var c=l.substr(0,d).trim(),u=l.substr(++d,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==n[c]&&(n[c]=function(e,t){try{return t(e)}catch(t){return e}}(u,s))}}return n},t.serialize=function(e,t,i){var a=i||{},s=a.encode||r;if("function"!=typeof s)throw TypeError("option encode is invalid");if(!n.test(e))throw TypeError("argument name is invalid");var o=s(t);if(o&&!n.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var d=a.maxAge-0;if(isNaN(d)||!isFinite(d))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(d)}if(a.domain){if(!n.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,i=/; */,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},340:(e,t,r)=>{var i;(()=>{var n={226:function(n,a){!function(s,o){"use strict";var l="function",d="undefined",c="object",u="string",p="major",m="model",g="name",h="type",f="vendor",y="version",_="architecture",v="console",w="mobile",b="tablet",S="smarttv",T="wearable",x="embedded",P="Amazon",C="Apple",M="ASUS",A="BlackBerry",R="Browser",E="Chrome",L="Firefox",N="Google",O="Huawei",k="Microsoft",D="Motorola",I="Opera",q="Samsung",B="Sharp",G="Sony",j="Xiaomi",U="Zebra",H="Facebook",F="Chromium OS",V="Mac OS",z=function(e,t){var r={};for(var i in e)t[i]&&t[i].length%2==0?r[i]=t[i].concat(e[i]):r[i]=e[i];return r},W=function(e){for(var t={},r=0;r<e.length;r++)t[e[r].toUpperCase()]=e[r];return t},K=function(e,t){return typeof e===u&&-1!==Y(t).indexOf(Y(e))},Y=function(e){return e.toLowerCase()},$=function(e,t){if(typeof e===u)return e=e.replace(/^\s\s*/,""),typeof t===d?e:e.substring(0,350)},Z=function(e,t){for(var r,i,n,a,s,d,u=0;u<t.length&&!s;){var p=t[u],m=t[u+1];for(r=i=0;r<p.length&&!s&&p[r];)if(s=p[r++].exec(e))for(n=0;n<m.length;n++)d=s[++i],typeof(a=m[n])===c&&a.length>0?2===a.length?typeof a[1]==l?this[a[0]]=a[1].call(this,d):this[a[0]]=a[1]:3===a.length?typeof a[1]!==l||a[1].exec&&a[1].test?this[a[0]]=d?d.replace(a[1],a[2]):void 0:this[a[0]]=d?a[1].call(this,d,a[2]):void 0:4===a.length&&(this[a[0]]=d?a[3].call(this,d.replace(a[1],a[2])):void 0):this[a]=d||o;u+=2}},J=function(e,t){for(var r in t)if(typeof t[r]===c&&t[r].length>0){for(var i=0;i<t[r].length;i++)if(K(t[r][i],e))return"?"===r?o:r}else if(K(t[r],e))return"?"===r?o:r;return e},Q={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},X={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[y,[g,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[y,[g,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[g,y],[/opios[\/ ]+([\w\.]+)/i],[y,[g,I+" Mini"]],[/\bopr\/([\w\.]+)/i],[y,[g,I]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/ ]?([\w\.]*)/i,/(avant |iemobile|slim)(?:browser)?[\/ ]?([\w\.]*)/i,/(ba?idubrowser)[\/ ]?([\w\.]+)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|qq|duckduckgo)\/([-\w\.]+)/i,/(heytap|ovi)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[g,y],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[y,[g,"UC"+R]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i],[y,[g,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[y,[g,"WeChat"]],[/konqueror\/([\w\.]+)/i],[y,[g,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[y,[g,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[y,[g,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[g,/(.+)/,"$1 Secure "+R],y],[/\bfocus\/([\w\.]+)/i],[y,[g,L+" Focus"]],[/\bopt\/([\w\.]+)/i],[y,[g,I+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[y,[g,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[y,[g,"Dolphin"]],[/coast\/([\w\.]+)/i],[y,[g,I+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[y,[g,"MIUI "+R]],[/fxios\/([-\w\.]+)/i],[y,[g,L]],[/\bqihu|(qi?ho?o?|360)browser/i],[[g,"360 "+R]],[/(oculus|samsung|sailfish|huawei)browser\/([\w\.]+)/i],[[g,/(.+)/,"$1 "+R],y],[/(comodo_dragon)\/([\w\.]+)/i],[[g,/_/g," "],y],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/ ]?([\w\.]+)/i],[g,y],[/(metasr)[\/ ]?([\w\.]+)/i,/(lbbrowser)/i,/\[(linkedin)app\]/i],[g],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[g,H],y],[/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/ ]([-\w\.]+)/i],[g,y],[/\bgsa\/([\w\.]+) .*safari\//i],[y,[g,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[y,[g,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[y,[g,E+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[g,E+" WebView"],y],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[y,[g,"Android "+R]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[g,y],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[y,[g,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[y,g],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[g,[y,J,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[g,y],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[g,"Netscape"],y],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[y,[g,L+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror|klar)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i,/panasonic;(viera)/i],[g,y],[/(cobalt)\/([\w\.]+)/i],[g,[y,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[_,"amd64"]],[/(ia32(?=;))/i],[[_,Y]],[/((?:i[346]|x)86)[;\)]/i],[[_,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[_,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[_,"armhf"]],[/windows (ce|mobile); ppc;/i],[[_,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[_,/ower/,"",Y]],[/(sun4\w)[;\)]/i],[[_,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[_,Y]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[m,[f,q],[h,b]],[/\b((?:s[cgp]h|gt|sm)-\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]([-\w]+)/i,/sec-(sgh\w+)/i],[m,[f,q],[h,w]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[m,[f,C],[h,w]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[m,[f,C],[h,b]],[/(macintosh);/i],[m,[f,C]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[m,[f,B],[h,w]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[m,[f,O],[h,b]],[/(?:huawei|honor)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[m,[f,O],[h,w]],[/\b(poco[\w ]+)(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite)?)(?: bui|\))/i],[[m,/_/g," "],[f,j],[h,w]],[/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[m,/_/g," "],[f,j],[h,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[m,[f,"OPPO"],[h,w]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[m,[f,"Vivo"],[h,w]],[/\b(rmx[12]\d{3})(?: bui|;|\))/i],[m,[f,"Realme"],[h,w]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[m,[f,D],[h,w]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[m,[f,D],[h,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[m,[f,"LG"],[h,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[m,[f,"LG"],[h,w]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[m,[f,"Lenovo"],[h,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[m,/_/g," "],[f,"Nokia"],[h,w]],[/(pixel c)\b/i],[m,[f,N],[h,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[m,[f,N],[h,w]],[/droid.+ (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[m,[f,G],[h,w]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[m,"Xperia Tablet"],[f,G],[h,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[m,[f,"OnePlus"],[h,w]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo[c-r]{2})( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[m,[f,P],[h,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[m,/(.+)/g,"Fire Phone $1"],[f,P],[h,w]],[/(playbook);[-\w\),; ]+(rim)/i],[m,f,[h,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[m,[f,A],[h,w]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[m,[f,M],[h,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[m,[f,M],[h,w]],[/(nexus 9)/i],[m,[f,"HTC"],[h,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[f,[m,/_/g," "],[h,w]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[m,[f,"Acer"],[h,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[m,[f,"Meizu"],[h,w]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[-_ ]?([-\w]*)/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[f,m,[h,w]],[/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[f,m,[h,b]],[/(surface duo)/i],[m,[f,k],[h,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[m,[f,"Fairphone"],[h,w]],[/(u304aa)/i],[m,[f,"AT&T"],[h,w]],[/\bsie-(\w*)/i],[m,[f,"Siemens"],[h,w]],[/\b(rct\w+) b/i],[m,[f,"RCA"],[h,b]],[/\b(venue[\d ]{2,7}) b/i],[m,[f,"Dell"],[h,b]],[/\b(q(?:mv|ta)\w+) b/i],[m,[f,"Verizon"],[h,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[m,[f,"Barnes & Noble"],[h,b]],[/\b(tm\d{3}\w+) b/i],[m,[f,"NuVision"],[h,b]],[/\b(k88) b/i],[m,[f,"ZTE"],[h,b]],[/\b(nx\d{3}j) b/i],[m,[f,"ZTE"],[h,w]],[/\b(gen\d{3}) b.+49h/i],[m,[f,"Swiss"],[h,w]],[/\b(zur\d{3}) b/i],[m,[f,"Swiss"],[h,b]],[/\b((zeki)?tb.*\b) b/i],[m,[f,"Zeki"],[h,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[f,"Dragon Touch"],m,[h,b]],[/\b(ns-?\w{0,9}) b/i],[m,[f,"Insignia"],[h,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[m,[f,"NextBook"],[h,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[f,"Voice"],m,[h,w]],[/\b(lvtel\-)?(v1[12]) b/i],[[f,"LvTel"],m,[h,w]],[/\b(ph-1) /i],[m,[f,"Essential"],[h,w]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[m,[f,"Envizen"],[h,b]],[/\b(trio[-\w\. ]+) b/i],[m,[f,"MachSpeed"],[h,b]],[/\btu_(1491) b/i],[m,[f,"Rotor"],[h,b]],[/(shield[\w ]+) b/i],[m,[f,"Nvidia"],[h,b]],[/(sprint) (\w+)/i],[f,m,[h,w]],[/(kin\.[onetw]{3})/i],[[m,/\./g," "],[f,k],[h,w]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[m,[f,U],[h,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[m,[f,U],[h,w]],[/smart-tv.+(samsung)/i],[f,[h,S]],[/hbbtv.+maple;(\d+)/i],[[m,/^/,"SmartTV"],[f,q],[h,S]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[f,"LG"],[h,S]],[/(apple) ?tv/i],[f,[m,C+" TV"],[h,S]],[/crkey/i],[[m,E+"cast"],[f,N],[h,S]],[/droid.+aft(\w)( bui|\))/i],[m,[f,P],[h,S]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[m,[f,B],[h,S]],[/(bravia[\w ]+)( bui|\))/i],[m,[f,G],[h,S]],[/(mitv-\w{5}) bui/i],[m,[f,j],[h,S]],[/Hbbtv.*(technisat) (.*);/i],[f,m,[h,S]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[f,$],[m,$],[h,S]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[h,S]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[f,m,[h,v]],[/droid.+; (shield) bui/i],[m,[f,"Nvidia"],[h,v]],[/(playstation [345portablevi]+)/i],[m,[f,G],[h,v]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[m,[f,k],[h,v]],[/((pebble))app/i],[f,m,[h,T]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[m,[f,C],[h,T]],[/droid.+; (glass) \d/i],[m,[f,N],[h,T]],[/droid.+; (wt63?0{2,3})\)/i],[m,[f,U],[h,T]],[/(quest( 2| pro)?)/i],[m,[f,H],[h,T]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[f,[h,x]],[/(aeobc)\b/i],[m,[f,P],[h,x]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+? mobile safari/i],[m,[h,w]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[m,[h,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[h,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[h,w]],[/(android[-\w\. ]{0,9});.+buil/i],[m,[f,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[y,[g,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[y,[g,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[g,y],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[y,g]],os:[[/microsoft (windows) (vista|xp)/i],[g,y],[/(windows) nt 6\.2; (arm)/i,/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i,/(windows)[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i],[g,[y,J,Q]],[/(win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[g,"Windows"],[y,J,Q]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/ios;fbsv\/([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[y,/_/g,"."],[g,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[g,V],[y,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[y,g],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[g,y],[/\(bb(10);/i],[y,[g,A]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[y,[g,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[y,[g,L+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[y,[g,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[y,[g,"watchOS"]],[/crkey\/([\d\.]+)/i],[y,[g,E+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[g,F],y],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[g,y],[/(sunos) ?([\w\.\d]*)/i],[[g,"Solaris"],y],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[g,y]]},ee=function(e,t){if(typeof e===c&&(t=e,e=o),!(this instanceof ee))return new ee(e,t).getResult();var r=typeof s!==d&&s.navigator?s.navigator:o,i=e||(r&&r.userAgent?r.userAgent:""),n=r&&r.userAgentData?r.userAgentData:o,a=t?z(X,t):X,v=r&&r.userAgent==i;return this.getBrowser=function(){var e,t={};return t[g]=o,t[y]=o,Z.call(t,i,a.browser),t[p]=typeof(e=t[y])===u?e.replace(/[^\d\.]/g,"").split(".")[0]:o,v&&r&&r.brave&&typeof r.brave.isBrave==l&&(t[g]="Brave"),t},this.getCPU=function(){var e={};return e[_]=o,Z.call(e,i,a.cpu),e},this.getDevice=function(){var e={};return e[f]=o,e[m]=o,e[h]=o,Z.call(e,i,a.device),v&&!e[h]&&n&&n.mobile&&(e[h]=w),v&&"Macintosh"==e[m]&&r&&typeof r.standalone!==d&&r.maxTouchPoints&&r.maxTouchPoints>2&&(e[m]="iPad",e[h]=b),e},this.getEngine=function(){var e={};return e[g]=o,e[y]=o,Z.call(e,i,a.engine),e},this.getOS=function(){var e={};return e[g]=o,e[y]=o,Z.call(e,i,a.os),v&&!e[g]&&n&&"Unknown"!=n.platform&&(e[g]=n.platform.replace(/chrome os/i,F).replace(/macos/i,V)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return i},this.setUA=function(e){return i=typeof e===u&&e.length>350?$(e,350):e,this},this.setUA(i),this};ee.VERSION="1.0.35",ee.BROWSER=W([g,y,p]),ee.CPU=W([_]),ee.DEVICE=W([m,f,h,v,w,S,b,T,x]),ee.ENGINE=ee.OS=W([g,y]),typeof a!==d?(n.exports&&(a=n.exports=ee),a.UAParser=ee):r.amdO?void 0!==(i=(function(){return ee}).call(t,r,t,e))&&(e.exports=i):typeof s!==d&&(s.UAParser=ee);var et=typeof s!==d&&(s.jQuery||s.Zepto);if(et&&!et.ua){var er=new ee;et.ua=er.getResult(),et.ua.get=function(){return er.getUA()},et.ua.set=function(e){er.setUA(e);var t=er.getResult();for(var r in t)et.ua[r]=t[r]}}}("object"==typeof window?window:this)}},a={};function s(e){var t=a[e];if(void 0!==t)return t.exports;var r=a[e]={exports:{}},i=!0;try{n[e].call(r.exports,r,r.exports,s),i=!1}finally{i&&delete a[e]}return r.exports}s.ab="//";var o=s(226);e.exports=o})()},4635:(e,t,r)=>{"use strict";function i(){throw Error('ImageResponse moved from "next/server" to "next/og" since Next.js 14, please import from "next/og" instead')}r.r(t),r.d(t,{ImageResponse:()=>i,NextRequest:()=>n.I,NextResponse:()=>a.x,URLPattern:()=>c,userAgent:()=>d,userAgentFromString:()=>l});var n=r(1669),a=r(8241),s=r(340),o=r.n(s);function l(e){return{...o()(e),isBot:void 0!==e&&/Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(e)}}function d({headers:e}){return l(e.get("user-agent")||void 0)}let c="undefined"==typeof URLPattern?void 0:URLPattern},300:(e,t,r)=>{"use strict";r.d(t,{Qq:()=>s,dN:()=>i,u7:()=>n,y3:()=>a});let i="nxtP",n="nxtI",a="x-prerender-revalidate",s="x-prerender-revalidate-if-generated",o={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"};({...o,GROUP:{serverOnly:[o.reactServerComponents,o.actionBrowser,o.appMetadataRoute,o.appRouteHandler,o.instrument],clientOnly:[o.serverSideRendering,o.appPagesBrowser],nonClientServerTarget:[o.middleware,o.api],app:[o.reactServerComponents,o.actionBrowser,o.appMetadataRoute,o.appRouteHandler,o.serverSideRendering,o.appPagesBrowser,o.shared,o.instrument]}})},6416:(e,t,r)=>{"use strict";r.d(t,{Y5:()=>a,cR:()=>n,qJ:()=>i});class i extends Error{constructor({page:e}){super(`The middleware "${e}" accepts an async API directly with the form:
  
  export function middleware(request, event) {
    return NextResponse.redirect('/new-location')
  }
  
  Read more: https://nextjs.org/docs/messages/middleware-new-signature
  `)}}class n extends Error{constructor(){super(`The request.page has been deprecated in favour of \`URLPattern\`.
  Read more: https://nextjs.org/docs/messages/middleware-request-page
  `)}}class a extends Error{constructor(){super(`The request.ua has been removed in favour of \`userAgent\` function.
  Read more: https://nextjs.org/docs/messages/middleware-parse-user-agent
  `)}}},9718:(e,t,r)=>{"use strict";function i(e){return e.replace(/\/$/,"")||"/"}function n(e){let t=e.indexOf("#"),r=e.indexOf("?"),i=r>-1&&(t<0||r<t);return i||t>-1?{pathname:e.substring(0,i?r:t),query:i?e.substring(r,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function a(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=n(e);return""+t+r+i+a}function s(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:r,query:i,hash:a}=n(e);return""+r+t+i+a}function o(e,t){if("string"!=typeof e)return!1;let{pathname:r}=n(e);return r===t||r.startsWith(t+"/")}function l(e,t){let r;let i=e.split("/");return(t||[]).some(t=>!!i[1]&&i[1].toLowerCase()===t.toLowerCase()&&(r=t,i.splice(1,1),e=i.join("/")||"/",!0)),{pathname:e,detectedLocale:r}}r.d(t,{c:()=>p});let d=/(?!^https?:\/\/)(127(?:\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)){3}|\[::1\]|localhost)/;function c(e,t){return new URL(String(e).replace(d,"localhost"),t&&String(t).replace(d,"localhost"))}let u=Symbol("NextURLInternal");class p{constructor(e,t,r){let i,n;"object"==typeof t&&"pathname"in t||"string"==typeof t?(i=t,n=r||{}):n=r||t||{},this[u]={url:c(e,i??n.base),options:n,basePath:""},this.analyze()}analyze(){var e,t,r,i,n;let a=function(e,t){var r,i;let{basePath:n,i18n:a,trailingSlash:s}=null!=(r=t.nextConfig)?r:{},d={pathname:e,trailingSlash:"/"!==e?e.endsWith("/"):s};n&&o(d.pathname,n)&&(d.pathname=function(e,t){if(!o(e,t))return e;let r=e.slice(t.length);return r.startsWith("/")?r:"/"+r}(d.pathname,n),d.basePath=n);let c=d.pathname;if(d.pathname.startsWith("/_next/data/")&&d.pathname.endsWith(".json")){let e=d.pathname.replace(/^\/_next\/data\//,"").replace(/\.json$/,"").split("/"),r=e[0];d.buildId=r,c="index"!==e[1]?"/"+e.slice(1).join("/"):"/",!0===t.parseData&&(d.pathname=c)}if(a){let e=t.i18nProvider?t.i18nProvider.analyze(d.pathname):l(d.pathname,a.locales);d.locale=e.detectedLocale,d.pathname=null!=(i=e.pathname)?i:d.pathname,!e.detectedLocale&&d.buildId&&(e=t.i18nProvider?t.i18nProvider.analyze(c):l(c,a.locales)).detectedLocale&&(d.locale=e.detectedLocale)}return d}(this[u].url.pathname,{nextConfig:this[u].options.nextConfig,parseData:!0,i18nProvider:this[u].options.i18nProvider}),s=function(e,t){let r;if((null==t?void 0:t.host)&&!Array.isArray(t.host))r=t.host.toString().split(":",1)[0];else{if(!e.hostname)return;r=e.hostname}return r.toLowerCase()}(this[u].url,this[u].options.headers);this[u].domainLocale=this[u].options.i18nProvider?this[u].options.i18nProvider.detectDomainLocale(s):function(e,t,r){if(e)for(let a of(r&&(r=r.toLowerCase()),e)){var i,n;if(t===(null==(i=a.domain)?void 0:i.split(":",1)[0].toLowerCase())||r===a.defaultLocale.toLowerCase()||(null==(n=a.locales)?void 0:n.some(e=>e.toLowerCase()===r)))return a}}(null==(t=this[u].options.nextConfig)?void 0:null==(e=t.i18n)?void 0:e.domains,s);let d=(null==(r=this[u].domainLocale)?void 0:r.defaultLocale)||(null==(n=this[u].options.nextConfig)?void 0:null==(i=n.i18n)?void 0:i.defaultLocale);this[u].url.pathname=a.pathname,this[u].defaultLocale=d,this[u].basePath=a.basePath??"",this[u].buildId=a.buildId,this[u].locale=a.locale??d,this[u].trailingSlash=a.trailingSlash}formatPathname(){var e;let t;return t=function(e,t,r,i){if(!t||t===r)return e;let n=e.toLowerCase();return!i&&(o(n,"/api")||o(n,"/"+t.toLowerCase()))?e:a(e,"/"+t)}((e={basePath:this[u].basePath,buildId:this[u].buildId,defaultLocale:this[u].options.forceLocale?void 0:this[u].defaultLocale,locale:this[u].locale,pathname:this[u].url.pathname,trailingSlash:this[u].trailingSlash}).pathname,e.locale,e.buildId?void 0:e.defaultLocale,e.ignorePrefix),(e.buildId||!e.trailingSlash)&&(t=i(t)),e.buildId&&(t=s(a(t,"/_next/data/"+e.buildId),"/"===e.pathname?"index.json":".json")),t=a(t,e.basePath),!e.buildId&&e.trailingSlash?t.endsWith("/")?t:s(t,"/"):i(t)}formatSearch(){return this[u].url.search}get buildId(){return this[u].buildId}set buildId(e){this[u].buildId=e}get locale(){return this[u].locale??""}set locale(e){var t,r;if(!this[u].locale||!(null==(r=this[u].options.nextConfig)?void 0:null==(t=r.i18n)?void 0:t.locales.includes(e)))throw TypeError(`The NextURL configuration includes no locale "${e}"`);this[u].locale=e}get defaultLocale(){return this[u].defaultLocale}get domainLocale(){return this[u].domainLocale}get searchParams(){return this[u].url.searchParams}get host(){return this[u].url.host}set host(e){this[u].url.host=e}get hostname(){return this[u].url.hostname}set hostname(e){this[u].url.hostname=e}get port(){return this[u].url.port}set port(e){this[u].url.port=e}get protocol(){return this[u].url.protocol}set protocol(e){this[u].url.protocol=e}get href(){let e=this.formatPathname(),t=this.formatSearch();return`${this.protocol}//${this.host}${e}${t}${this.hash}`}set href(e){this[u].url=c(e),this.analyze()}get origin(){return this[u].url.origin}get pathname(){return this[u].url.pathname}set pathname(e){this[u].url.pathname=e}get hash(){return this[u].url.hash}set hash(e){this[u].url.hash=e}get search(){return this[u].url.search}set search(e){this[u].url.search=e}get password(){return this[u].url.password}set password(e){this[u].url.password=e}get username(){return this[u].url.username}set username(e){this[u].url.username=e}get basePath(){return this[u].basePath}set basePath(e){this[u].basePath=e.startsWith("/")?e:`/${e}`}toString(){return this.href}toJSON(){return this.href}[Symbol.for("edge-runtime.inspect.custom")](){return{href:this.href,origin:this.origin,protocol:this.protocol,username:this.username,password:this.password,host:this.host,hostname:this.hostname,port:this.port,pathname:this.pathname,search:this.search,searchParams:this.searchParams,hash:this.hash}}clone(){return new p(String(this),this[u].options)}}},7217:(e,t,r)=>{"use strict";r.d(t,{g:()=>i});class i{static get(e,t,r){let i=Reflect.get(e,t,r);return"function"==typeof i?i.bind(e):i}static set(e,t,r,i){return Reflect.set(e,t,r,i)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},938:(e,t,r)=>{"use strict";r.d(t,{Q7:()=>i.stringifyCookie,nV:()=>i.ResponseCookies,qC:()=>i.RequestCookies});var i=r(5945)},1669:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var i=r(9718),n=r(6329),a=r(6416),s=r(938);let o=Symbol("internal request");class l extends Request{constructor(e,t={}){let r="string"!=typeof e&&"url"in e?e.url:String(e);(0,n.r4)(r),e instanceof Request?super(e,t):super(r,t);let a=new i.c(r,{headers:(0,n.lb)(this.headers),nextConfig:t.nextConfig});this[o]={cookies:new s.qC(this.headers),geo:t.geo||{},ip:t.ip,nextUrl:a,url:a.toString()}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,geo:this.geo,ip:this.ip,nextUrl:this.nextUrl,url:this.url,bodyUsed:this.bodyUsed,cache:this.cache,credentials:this.credentials,destination:this.destination,headers:Object.fromEntries(this.headers),integrity:this.integrity,keepalive:this.keepalive,method:this.method,mode:this.mode,redirect:this.redirect,referrer:this.referrer,referrerPolicy:this.referrerPolicy,signal:this.signal}}get cookies(){return this[o].cookies}get geo(){return this[o].geo}get ip(){return this[o].ip}get nextUrl(){return this[o].nextUrl}get page(){throw new a.cR}get ua(){throw new a.Y5}get url(){return this[o].url}}},8241:(e,t,r)=>{"use strict";r.d(t,{x:()=>c});var i=r(938),n=r(9718),a=r(6329),s=r(7217);let o=Symbol("internal response"),l=new Set([301,302,303,307,308]);function d(e,t){var r;if(null==e?void 0:null==(r=e.request)?void 0:r.headers){if(!(e.request.headers instanceof Headers))throw Error("request.headers must be an instance of Headers");let r=[];for(let[i,n]of e.request.headers)t.set("x-middleware-request-"+i,n),r.push(i);t.set("x-middleware-override-headers",r.join(","))}}class c extends Response{constructor(e,t={}){super(e,t);let r=this.headers,l=new Proxy(new i.nV(r),{get(e,n,a){switch(n){case"delete":case"set":return(...a)=>{let s=Reflect.apply(e[n],e,a),o=new Headers(r);return s instanceof i.nV&&r.set("x-middleware-set-cookie",s.getAll().map(e=>(0,i.Q7)(e)).join(",")),d(t,o),s};default:return s.g.get(e,n,a)}}});this[o]={cookies:l,url:t.url?new n.c(t.url,{headers:(0,a.lb)(r),nextConfig:t.nextConfig}):void 0}}[Symbol.for("edge-runtime.inspect.custom")](){return{cookies:this.cookies,url:this.url,body:this.body,bodyUsed:this.bodyUsed,headers:Object.fromEntries(this.headers),ok:this.ok,redirected:this.redirected,status:this.status,statusText:this.statusText,type:this.type}}get cookies(){return this[o].cookies}static json(e,t){let r=Response.json(e,t);return new c(r.body,r)}static redirect(e,t){let r="number"==typeof t?t:(null==t?void 0:t.status)??307;if(!l.has(r))throw RangeError('Failed to execute "redirect" on "response": Invalid status code');let i="object"==typeof t?t:{},n=new Headers(null==i?void 0:i.headers);return n.set("Location",(0,a.r4)(e)),new c(null,{...i,headers:n,status:r})}static rewrite(e,t){let r=new Headers(null==t?void 0:t.headers);return r.set("x-middleware-rewrite",(0,a.r4)(e)),d(t,r),new c(null,{...t,headers:r})}static next(e){let t=new Headers(null==e?void 0:e.headers);return t.set("x-middleware-next","1"),d(e,t),new c(null,{...e,headers:t})}}},6329:(e,t,r)=>{"use strict";r.d(t,{EK:()=>n,LI:()=>l,l$:()=>a,lb:()=>s,r4:()=>o});var i=r(300);function n(e){let t=new Headers;for(let[r,i]of Object.entries(e))for(let e of Array.isArray(i)?i:[i])void 0!==e&&("number"==typeof e&&(e=e.toString()),t.append(r,e));return t}function a(e){var t,r,i,n,a,s=[],o=0;function l(){for(;o<e.length&&/\s/.test(e.charAt(o));)o+=1;return o<e.length}for(;o<e.length;){for(t=o,a=!1;l();)if(","===(r=e.charAt(o))){for(i=o,o+=1,l(),n=o;o<e.length&&"="!==(r=e.charAt(o))&&";"!==r&&","!==r;)o+=1;o<e.length&&"="===e.charAt(o)?(a=!0,o=n,s.push(e.substring(t,i)),t=o):o=i+1}else o+=1;(!a||o>=e.length)&&s.push(e.substring(t,e.length))}return s}function s(e){let t={},r=[];if(e)for(let[i,n]of e.entries())"set-cookie"===i.toLowerCase()?(r.push(...a(n)),t[i]=1===r.length?r[0]:r):t[i]=n;return t}function o(e){try{return String(new URL(String(e)))}catch(t){throw Error(`URL is malformed "${String(e)}". Please use only absolute URLs - https://nextjs.org/docs/messages/middleware-relative-urls`,{cause:t})}}function l(e,t){for(let r of[i.dN,i.u7])e!==r&&e.startsWith(r)&&t(e.substring(r.length))}},8488:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getTestReqInfo:function(){return s},withRequest:function(){return a}});let i=new(r(2067)).AsyncLocalStorage;function n(e,t){let r=t.header(e,"next-test-proxy-port");if(r)return{url:t.url(e),proxyPort:Number(r),testData:t.header(e,"next-test-data")||""}}function a(e,t,r){let a=n(e,t);return a?i.run(a,r):r()}function s(e,t){return i.getStore()||(e&&t?n(e,t):void 0)}},375:(e,t,r)=>{"use strict";var i=r(6195).Buffer;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{handleFetch:function(){return o},interceptFetch:function(){return l},reader:function(){return a}});let n=r(8488),a={url:e=>e.url,header:(e,t)=>e.headers.get(t)};async function s(e,t){let{url:r,method:n,headers:a,body:s,cache:o,credentials:l,integrity:d,mode:c,redirect:u,referrer:p,referrerPolicy:m}=t;return{testData:e,api:"fetch",request:{url:r,method:n,headers:[...Array.from(a),["next-test-stack",function(){let e=(Error().stack??"").split("\n");for(let t=1;t<e.length;t++)if(e[t].length>0){e=e.slice(t);break}return(e=(e=(e=e.filter(e=>!e.includes("/next/dist/"))).slice(0,5)).map(e=>e.replace("webpack-internal:///(rsc)/","").trim())).join("    ")}()]],body:s?i.from(await t.arrayBuffer()).toString("base64"):null,cache:o,credentials:l,integrity:d,mode:c,redirect:u,referrer:p,referrerPolicy:m}}}async function o(e,t){let r=(0,n.getTestReqInfo)(t,a);if(!r)return e(t);let{testData:o,proxyPort:l}=r,d=await s(o,t),c=await e(`http://localhost:${l}`,{method:"POST",body:JSON.stringify(d),next:{internal:!0}});if(!c.ok)throw Error(`Proxy request failed: ${c.status}`);let u=await c.json(),{api:p}=u;switch(p){case"continue":return e(t);case"abort":case"unhandled":throw Error(`Proxy request aborted [${t.method} ${t.url}]`)}return function(e){let{status:t,headers:r,body:n}=e.response;return new Response(n?i.from(n,"base64"):null,{status:t,headers:new Headers(r)})}(u)}function l(e){return r.g.fetch=function(t,r){var i;return(null==r?void 0:null==(i=r.next)?void 0:i.internal)?e(t,r):o(e,new Request(t,r))},()=>{r.g.fetch=e}}},4177:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{interceptTestApis:function(){return a},wrapRequestHandler:function(){return s}});let i=r(8488),n=r(375);function a(){return(0,n.interceptFetch)(r.g.fetch)}function s(e){return(t,r)=>(0,i.withRequest)(t,n.reader,()=>e(t,r))}},5228:(e,t,r)=>{"use strict";r.d(t,{P:()=>s});let i=Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available");class n{disable(){throw i}getStore(){}run(){throw i}exit(){throw i}enterWith(){throw i}}let a=globalThis.AsyncLocalStorage;function s(){return a?new a:new n}},5303:(e,t,r)=>{"use strict";r.d(t,{F:()=>i});let i=(0,r(5228).P)()},5558:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(5228).P)()},3981:e=>{"use strict";e.exports=JSON.parse('{"site":{"name":"periodhub.health","title":"periodhub.health - Your Guide to Menstrual Wellness","description":"Your compassionate guide to navigating menstrual pain with effective solutions, supportive resources, and a path to better menstrual health."},"navigation":{"home":"Home","articles":"Articles & Downloads","therapies":"Therapies","instantRelief":"Immediate Relief","naturalTherapies":"Natural Care","culturalCharms":"Cultural Comforts","scenarioSolutions":"Scenario Solutions","downloads":"PDF Downloads","interactiveTools":"Interactive Solutions","symptomAssessment":"Symptom Assessment","painTracker":"Pain Tracker","healthGuide":"Health Guide"},"homepage":{"hero":{"headline":"Find Relief, Understand Your Cycle.","subheadline":"Your compassionate guide to navigating menstrual pain with effective solutions and supportive resources.","bodyCopy":"Welcome to periodhub.health – your trusted partner for menstrual wellness. We offer evidence-based insights, practical tools, and compassionate support to help you understand, manage, and find relief from menstrual discomfort. Whether you need quick remedies, long-term strategies, or a deeper understanding of your body, we\'re here for you.","ctaExplore":"Explore Solutions","ctaCheckSymptoms":"Start Your Symptom Check","imageAlt":"A woman in a warm, caring environment expressing understanding and compassion","ctaDiscoverNatural":"Discover Natural Therapies"},"modules":{"title":"Discover Your Path to Comfort","instantRelief":{"title":"Instant Relief","description":"Quick techniques and remedies to alleviate period pain when you need it most. Find methods you can use right now for immediate comfort."},"interactiveSolutions":{"title":"Interactive Solutions","description":"Engaging tools and personalized insights to help you manage your menstrual health proactively. Use our symptom checker and resources for tailored guidance."},"dailyConditioning":{"title":"Daily Conditioning","description":"Lifestyle adjustments and routines to build long-term resilience against menstrual discomfort and support overall wellness throughout your cycle."}},"featuredContent":{"title":"Featured Articles","viewAll":"View All Articles","readMore":"Read more"}},"articlesPage":{"title":"Articles & Guides","description":"Explore our collection of articles to learn more about menstrual health and pain relief."},"articleList":{"publishedOn":"Published on {date, date, long}","readMore":"Read more","noArticles":"No articles found. Check back soon!"},"articleDetail":{"backToList":"Back to Articles","publishedOn":"Published on {date, date, long}","notFoundTitle":"Article Not Found","notFoundDescription":"Sorry, we could not find the article you are looking for."},"therapiesPage":{"title":"Relief Therapies","description":"Discover various therapies and methods to manage and alleviate period pain."},"therapyList":{"lastUpdated":"Last updated on {date, date, long}","learnMore":"Learn more","noTherapies":"No therapies found. Check back soon!"},"therapyDetail":{"backToList":"Back to Therapies","lastUpdated":"Last updated on {date, date, long}","notFoundTitle":"Therapy Not Found","notFoundDescription":"Sorry, we could not find the therapy you are looking for."},"SpecialTherapiesPage":{"title":"Special Therapies","description":"Explore traditional and modern special therapies to provide more options for your menstrual health.","introduction":"Special therapies combine traditional wisdom with modern science to provide unique relief methods for menstrual discomfort. These therapies focus on holistic health, helping you find the most suitable treatment approach for yourself.","therapyCards":{"title":"Featured Therapies","viewAll":"View All"},"videoSection":{"title":"Therapy Demonstration Videos","description":"Learn proper therapy techniques and precautions through professional video demonstrations."},"disclaimer":"The therapy information provided here is for reference only and cannot replace professional medical advice. Please consult medical professionals before trying any new treatment methods."},"immediateReliefPage":{"title":"Immediate Relief Solutions for Period Pain","description":"Explore quick and effective methods to find relief from period pain when you need it most. Learn about techniques you can apply right now.","introTitle":"When Period Pain Strikes...","introText":"When period pain strikes, you need fast, effective ways to find comfort. This section is dedicated to immediate relief solutions – methods you can turn to right now to help alleviate acute menstrual cramps and discomfort.","typesTitle":"Types of Immediate Relief","heatTherapy":"Heat Therapy","gentleMovement":"Gentle Movement & Breathing","acupressure":"Acupressure","otcOptions":"Over-the-Counter Options","findingWhatWorksTitle":"Finding What Works for You","findingWhatWorksText":"Experimentation is key to discovering which immediate relief methods are most effective for your unique body and pain type. Consider combining different approaches for enhanced comfort.","contentSectionTitle":"Relevant Articles & Therapies"},"naturalTherapiesPage":{"title":"Natural Therapies & Daily Conditioning for Menstrual Wellness","description":"Discover gentle, natural approaches and lifestyle adjustments for long-term menstrual wellness and reducing pain.","introTitle":"Cultivating Long-Term Health","introText":"Beyond immediate relief, cultivating long-term menstrual wellness involves integrating natural therapies and mindful daily habits into your routine. This section explores holistic approaches that can help reduce the frequency and severity of period pain over time and support your overall well-being throughout your cycle.","holisticApproachesTitle":"Holistic Approaches for Long-Term Comfort","dietNutrition":"Diet & Nutrition","herbalRemedies":"Herbal Remedies & Supplements","movementExercise":"Movement & Exercise","mindBodyPractices":"Mind-Body Practices","traditionalPractices":"Traditional Practices","consistencyIsKeyTitle":"Consistency is Key","consistencyIsKeyText":"Integrating these natural approaches takes time and consistency. Be patient with yourself and focus on building sustainable habits that support your body throughout your entire cycle, not just during your period.","contentSectionTitle":"Relevant Therapies & Guides"},"culturalCharmsPage":{"title":"Exploring Cultural Comfort: Charms and Traditional Healing Symbols","description":"Explore the historical and cultural significance of charms, symbols, and traditional practices used for comfort and spiritual support.","introTitle":"A Journey Through Cultural Traditions","introText":"Throughout history and across diverse cultures, people have turned to symbols, objects, and traditional practices for comfort, protection, and support during times of vulnerability, including during menstruation. These practices are deeply rooted in cultural belief systems and folklore.","understandingTitle":"Understanding Cultural Healing Traditions","understandingText":"Many cultures have traditions that involve using specific items or performing certain rituals believed to influence well-being, bring good fortune, or ward off negative energy. For some, this extends to seeking comfort or relief during challenging physical experiences like menstrual discomfort.","disclaimerTitle":"Cultural Support Disclaimer","disclaimerText":"The information presented here regarding cultural charms, traditional practices, and associated beliefs is for cultural and informational purposes only. These practices are rooted in tradition and belief systems and are not intended to be, and should not be interpreted as, a substitute for professional medical advice, diagnosis, or treatment.","mediaAreaTitle":"Cultural Symbols and Imagery","imagePlaceholder":"Image/Media Placeholder","promptSuggestionPrefix":"AI Prompt: ","introduction":"Throughout history and across diverse cultures, people have turned to symbols, objects, and traditional practices for comfort, protection, and support during times of vulnerability, including during menstruation. These practices are deeply rooted in cultural belief systems and folklore.","multimediaTitle":"Cultural Symbols and Imagery","contentSection1Description":"Explore various traditional symbols and objects used across cultures for comfort and spiritual support.","disclaimer":"The information presented here regarding cultural charms, traditional practices, and associated beliefs is for cultural and informational purposes only. These practices are rooted in tradition and belief systems and are not intended to be, and should not be interpreted as, a substitute for professional medical advice, diagnosis, or treatment.","backHome":"Back to Home"},"interactiveToolsPage":{"title":"Interactive Tools for Menstrual Health Management","description":"Use our interactive tools to better understand your symptoms, track your pain patterns, and receive personalized recommendations.","symptomAssessment":{"title":"Symptom Assessment Tool","description":"Answer a few questions about your symptoms to receive personalized recommendations for managing your menstrual pain.","startButton":"Start Assessment","questions":{"painLocation":"Where do you feel pain during your period?","painIntensity":"How would you rate your pain intensity?","painDuration":"How long does your pain typically last?","accompaniedSymptoms":"Which symptoms accompany your menstrual pain?","reliefMethods":"Which methods have you tried for relief?"},"results":{"title":"Your Personalized Recommendations","disclaimer":"These recommendations are based on your responses and are for informational purposes only. They are not a substitute for professional medical advice.","tryAgain":"Try Again","saveResults":"Save Results"}},"painTracker":{"title":"Pain Tracking System","description":"Record and monitor your menstrual pain patterns over time to identify triggers and effective relief methods.","startButton":"Start Tracking","addEntry":"Add New Entry","viewHistory":"View History","entryForm":{"date":"Date","painLevel":"Pain Level","location":"Pain Location","symptoms":"Associated Symptoms","remedies":"Remedies Used","effectiveness":"Effectiveness","notes":"Notes","save":"Save Entry","cancel":"Cancel"},"insights":{"title":"Your Pain Insights","description":"Based on your tracking history, here are some patterns we\'ve noticed:","noData":"Not enough data yet. Continue tracking to see insights."},"assessment":{"title":"Symptom Assessment Tool","subtitle":"Quickly identify pain types through professional questionnaires and receive precise personalized recommendations.","start":{"title":"Before Starting","description":"Please ensure you\'re in a quiet, private environment where you can focus on answering the questions. This assessment will help you better understand your symptom patterns.","feature1":"Professional symptom analysis","feature2":"Personalized recommendations","feature3":"Scientific assessment methods","feature4":"Instant result feedback","startButton":"Start Assessment","disclaimer":"This assessment is for reference only and cannot replace professional medical diagnosis."},"progress":{"questionOf":"Question {current} of {total}"},"navigation":{"previous":"Previous","next":"Next","skip":"Skip","finish":"Complete Assessment"},"result":{"title":"Assessment Results","yourScore":"Your Score","severity":"Severity","summary":"Summary","recommendations":"Recommendations","timeframe":"Timeframe:","actionSteps":"Action Steps","retakeAssessment":"Retake Assessment","saveResults":"Save Results","nextSteps":{"trackSymptoms":"Use pain tracker to record symptoms","tryRecommendations":"Try recommended relief methods","consultDoctor":"If symptoms persist or worsen, consult a doctor"}},"severity":{"mild":"Mild","moderate":"Moderate","severe":"Severe","emergency":"Emergency"},"priority":{"high":"High Priority","medium":"Medium Priority","low":"Low Priority"},"messages":{"assessmentComplete":"Assessment Complete","assessmentCompleteDesc":"Your symptom assessment is complete. Please review the results and recommendations.","assessmentFailed":"Assessment Failed","assessmentFailedDesc":"An error occurred during assessment. Please try again.","resultsSaved":"Results Saved","resultsSavedDesc":"Your assessment results have been saved to local storage."},"resultMessages":{"emergency":"Your symptoms are quite severe, we recommend consulting a medical professional as soon as possible.","emergencySummary":"Assessment indicates you may need professional medical attention.","severe":"Your symptoms are quite severe, we recommend adopting comprehensive management strategies.","severeSummary":"Your symptoms require active management and possible medical intervention.","moderate":"You have moderate symptoms that can be managed through various methods.","moderateSummary":"Your symptoms are manageable with recommended relief strategies.","mild":"Your symptoms are relatively mild and can be well managed through simple self-care.","mildSummary":"Your symptoms are mild and can be improved through lifestyle adjustments."},"recommendations":{"emergencyMedical":{"title":"Seek Immediate Medical Care","description":"Your symptoms may require professional medical evaluation and treatment","timeframe":"Immediately","actionSteps":["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]},"painManagement":{"title":"Pain Management Strategies","description":"Multiple methods can help relieve menstrual pain","timeframe":"Immediately available","actionSteps":["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]},"lifestyleChanges":{"title":"Lifestyle Adjustments","description":"Long-term lifestyle changes can significantly improve symptoms","timeframe":"2-3 months to see effects","actionSteps":["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]},"selfcarePractices":{"title":"Self-Care Practices","description":"Daily self-care can help you better manage symptoms","timeframe":"Ongoing","actionSteps":["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]}}}},"personalizedInsights":{"title":"Personalized Insights (Future)","description":"Unlock deeper insights into your menstrual health patterns based on your tracked data and assessments. (Coming Soon)"},"periodPainAssessment":{"title":"Period Pain Assessment Tool","description":"Answer a few simple questions to understand your period pain type and severity, and get personalized health recommendations.","cta":"Quick Assessment","tool":{"title":"Period Pain Assessment Tool","subtitle":"Answer a few simple questions to understand your period pain type and severity.","intensityQuestion":"How intense is your period pain?","intensityOptions":{"mild":"Mild (tolerable, doesn\'t affect daily activities)","moderate":"Moderate (affects some activities, but manageable)","severe":"Severe (completely affects daily activities, requires rest)"},"onsetQuestion":"When did your period pain start?","onsetOptions":{"recent":"Soon after menarche (within 1-2 years)","later":"Started later (years after menarche)"},"symptomsQuestion":"Do you have any of the following symptoms? (Multiple choice)","symptoms":{"fever":"Fever (temperature above 38\xb0C)","vomiting":"Severe vomiting","dizziness":"Fainting or severe dizziness","bleeding":"Abnormal bleeding (heavy flow or large clots)","nonMenstrual":"Pelvic pain even when not menstruating"},"assessButton":"Assess if Medical Care is Needed","resetButton":"Reset Assessment","moreInfoButton":"View More Teen Health Guides","disclaimer":"⚠️ This tool is for reference only and cannot replace professional medical diagnosis. If you have concerns, please consult a doctor.","medicalDisclaimer":"Medical Disclaimer","result":{"resultTitle":"Assessment Result","consultAdvice":"We recommend consulting a doctor or gynecologist as soon as possible for professional medical advice and treatment options.","validationMessage":"Please select pain intensity and onset time first","assessments":{"severe_symptoms":"Based on the symptoms you selected (such as fever, severe vomiting, fainting, or abnormal bleeding), we recommend consulting a doctor immediately as these may be warning signs requiring medical evaluation.","severe_late":"Severe period pain that starts years after menarche may indicate secondary dysmenorrhea. We recommend consulting a doctor to rule out potential gynecological issues.","severe_early":"Your period pain is quite severe. While it may be primary dysmenorrhea, severe pain affecting daily life warrants medical evaluation for effective pain management.","moderate_late":"Moderate period pain starting years after menarche warrants consulting a doctor to rule out secondary dysmenorrhea.","normal":"Based on the information you provided, your period pain appears to be within the common range and may be primary dysmenorrhea. We recommend trying natural relief methods such as heat therapy, light exercise, and healthy dietary habits. If symptoms worsen or become abnormal, please consult a doctor promptly."}}}},"constitutionTest":{"title":"TCM Constitution Test","description":"Discover your TCM constitution type through 8 questions and get personalized acupoint, diet, and lifestyle recommendations","cta":"Start Test"},"breathingExercise":{"title":"4-7-8 Breathing Exercise","subtitle":"Natural pain relief through nervous system regulation","phases":{"inhale":"Inhale","hold":"Hold","exhale":"Exhale"},"instructions":"How to practice:","startButton":"\uD83E\uDEC1 Start Guided Practice","stopButton":"Stop Practice","practiceAgain":"Practice Again","completed":"✅ One cycle completed!","currentPhase":"Current:","benefits":{"title":"Scientific Benefits:","painPerception":"Pain Perception","muscleTension":"Muscle Tension","relaxation":"Relaxation"},"tip":"\uD83D\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.","timeUnit":"s","description":"[EN] 基于科学验证的4-7-8呼吸法，通过调节神经系统快速缓解疼痛。无需下载，立即开始练习。","usageTips":{"title":"Usage Tips:","bestTiming":{"title":"Best Timing:","items":["When pain just starts","When feeling anxious","Before sleep for relaxation"]},"precautions":{"title":"Precautions:","items":["Find comfortable sitting or lying position","Beginners: 3-4 cycles are enough","Stop if feeling dizzy"]}}},"embeddedPainAssessment":{"title":"\uD83D\uDCA1 Quick Pain Assessment","subtitle":"Understand your pain level in 1 minute and get initial recommendations","question":"How intense is your menstrual pain?","selectIntensityFirst":"Please select pain intensity first","options":{"mild":"Mild (tolerable, doesn\'t affect daily activities)","moderate":"Moderate (affects some activities, but manageable)","severe":"Severe (completely affects daily activities, need rest)"},"buttons":{"getAdvice":"Get Advice","detailedAssessment":"Detailed Assessment","testAgain":"Test Again","fullAssessment":"Full Assessment"},"resultTitle":"Assessment Result","results":{"mild":"Your menstrual pain is mild. You can try natural relief methods like heat therapy and light exercise.","moderate":"Your menstrual pain is moderate. Consider combining multiple relief methods, and over-the-counter pain medication if needed.","severe":"Your menstrual pain is severe. We recommend consulting a doctor for professional assessment and treatment advice."},"disclaimer":"⚠️ This tool is for reference only and cannot replace professional medical advice"},"searchBox":{"placeholder":"Search articles...","matchTypes":{"title":"Title","summary":"Summary","tag":"Tag","content":"Content"},"noResults":"No articles found"},"painDifferentialDiagnosis":{"title":"Pain Differential Diagnosis","description":"Advanced diagnostic tool to help differentiate between various types of menstrual pain and identify potential underlying causes.","startButton":"Start Diagnosis"},"toolsIntroduction":"Our interactive tools are designed to empower you with knowledge and personalized strategies for managing your menstrual health. By understanding your unique symptoms and patterns, you can make informed decisions and find more effective relief.","developmentNote":"We are continuously developing new tools and resources. Check back often for updates!"},"interactiveTools":{"common":{"loading":"Loading...","error":"An error occurred, please try again","submit":"Submit","cancel":"Cancel","save":"Save","reset":"Reset","back":"Back","next":"Next","previous":"Previous","close":"Close","startNow":"Start Now","tryNow":"Try Now","learnMore":"Learn More"},"navigation":{"backToTools":"Back to Tools","toolsTitle":"Interactive Tools","toolsDescription":"Professional menstrual health management tools to help you better understand and manage your health"},"categories":{"assessment":"Assessment","tracking":"Tracking","constitutionAssessment":"Constitution Assessment","healthAssessment":"Health Assessment"},"difficulty":{"easy":"Easy","medium":"Medium","hard":"Hard"},"estimatedTime":{"5to10min":"5-10 minutes","2to3minDaily":"2-3 minutes daily","5to8min":"5-8 minutes","3to5min":"3-5 minutes"},"medicalDisclaimer":{"title":"Medical Disclaimer","text":"This tool is for reference only and cannot replace professional medical advice, diagnosis, or treatment. Please consult healthcare professionals for any health concerns.","shortText":"⚠️ This tool is for reference only and cannot replace professional medical diagnosis. If you have concerns, please consult a doctor."},"periodPainAssessment":{"title":"Period Pain Assessment Tool","description":"Answer a few simple questions to understand your period pain type and severity, and get personalized health recommendations.","cta":"Quick Assessment","subtitle":"Answer a few simple questions to understand your period pain type and severity.","questions":{"intensity":{"title":"How intense is your period pain?","options":{"mild":"Mild (tolerable, doesn\'t affect daily activities)","moderate":"Moderate (affects some activities, but manageable)","severe":"Severe (completely affects daily activities, requires rest)"}},"onset":{"title":"When did your period pain start?","options":{"recent":"Soon after menarche (within 1-2 years)","later":"Started later (years after menarche)"}},"symptoms":{"title":"Do you have any of the following symptoms? (Multiple choice)","options":{"fever":"Fever (temperature above 38\xb0C)","vomiting":"Severe vomiting","dizziness":"Fainting or severe dizziness","bleeding":"Abnormal bleeding (heavy flow or large clots)","nonMenstrual":"Pelvic pain even when not menstruating"}}},"actions":{"assess":"Assess if Medical Care is Needed","reset":"Reset Assessment","moreInfo":"View More Teen Health Guides"},"results":{"title":"Assessment Result","consultAdvice":"We recommend consulting a doctor or gynecologist as soon as possible for professional medical advice and treatment options.","validationMessage":"Please select pain intensity and onset time first","assessments":{"severe_symptoms":"Based on the symptoms you selected (such as fever, severe vomiting, fainting, or abnormal bleeding), we recommend consulting a doctor immediately as these may be warning signs requiring medical evaluation.","severe_late":"Severe period pain that starts years after menarche may indicate secondary dysmenorrhea. We recommend consulting a doctor to rule out potential gynecological issues.","severe_early":"Your period pain is quite severe. While it may be primary dysmenorrhea, severe pain affecting daily life warrants medical evaluation for effective pain management.","moderate_late":"Moderate period pain starting years after menarche warrants consulting a doctor to rule out secondary dysmenorrhea.","normal":"Based on the information you provided, your period pain appears to be within the common range and may be primary dysmenorrhea. We recommend trying natural relief methods such as heat therapy, light exercise, and healthy dietary habits. If symptoms worsen or become abnormal, please consult a doctor promptly."}}},"constitutionTest":{"title":"TCM Constitution Test","description":"Discover your TCM constitution type through 8 questions and get personalized acupoint, diet, and lifestyle recommendations","cta":"Start Test","subtitle":"Discover your TCM constitution type through professional questionnaire and get personalized health recommendations","features":{"quick":{"title":"Quick & Easy","description":"Only takes 5-8 minutes"},"professional":{"title":"Professional","description":"Based on TCM theory"},"personalized":{"title":"Personalized","description":"Tailored to your constitution"},"practical":{"title":"Practical","description":"Acupoint, diet, lifestyle tips"}},"instructions":{"title":"Test Instructions","item1":"Answer based on your condition in the past 3 months","item2":"Choose the option that best describes you","item3":"If unsure, choose the relatively more suitable option","item4":"Results are for reference only, not medical diagnosis"},"navigation":{"startTest":"Start Test","nextQuestion":"Next","previousQuestion":"Previous","completeTest":"Complete Test","retakeTest":"Retake Test","previous":"Previous","next":"Next"},"painScale":{"title":"Pain Level: ","reference":"Pain Level Reference","levels":{"none":"None","mild":"Mild","moderate":"Moderate","severe":"Severe","extreme":"Extreme"},"descriptions":{"0-2":"No pain or slight discomfort","3-4":"Mild pain, tolerable","5-7":"Moderate pain, affects activities","8-10":"Severe pain, unbearable"}},"progress":{"questionOf":"Question {current} of {total}","complete":"Complete"},"result":{"title":"Test Results","subtitle":"Your TCM Constitution Type Analysis","match":"Match","constitutionFeatures":"Constitution Features","commonSymptoms":"Common Symptoms","menstrualFeatures":"Menstrual Features"},"recommendations":{"acupoints":{"title":"Acupoint Therapy Recommendations","primaryAcupoints":"Primary Acupoints","location":"Location: ","function":"Function: ","method":"Method: ","guidelines":"Massage Guidelines","technique":"Technique: ","frequency":"Frequency: ","duration":"Duration: "},"dietary":{"title":"Dietary Recommendations","beneficialFoods":"Beneficial Foods","foodsToAvoid":"Foods to Avoid","dietaryPrinciples":"Dietary Principles"},"lifestyle":{"title":"Scenario-based Lifestyle Recommendations","description":"Based on your constitution type, here are personalized recommendations for different life scenarios","reminder":"Reminder: ","reminderText":"These recommendations require consistent practice to see results. It\'s recommended to combine with professional medical guidance."},"menstrualPain":{"title":"Menstrual Pain Specific Recommendations","acupointTherapy":"Acupoint Therapy","lifestyleAdjustments":"Lifestyle Adjustments"}},"messages":{"testComplete":"Test Complete","testCompleteDesc":"Your personalized constitution analysis has been generated","testFailed":"Test Failed","testFailedDesc":"Unable to generate test results, please try again"},"emergencyKit":{"title":"Personalized Emergency Kit Recommendations","description":"Based on your constitution characteristics, we recommend a personalized emergency kit item list. Be prepared for a more comfortable period.","priority":{"high":"Essential","medium":"Recommended","low":"Optional"},"packingTips":"\uD83D\uDCE6 Packing Tips:","packingAdvice":"Prioritize \\"Essential\\" items, choose \\"Recommended\\" and \\"Optional\\" items based on outing duration and scenarios. Consider preparing a dedicated small bag for easy access."},"articles":{"title":"Recommended Health Articles","readMore":"Read More"},"communication":{"title":"Communication Template Assistant","description":"Communication with people around you is important when experiencing menstrual discomfort. These templates can help you better express your needs and seek understanding.","styles":{"intimate":"Intimate","casual":"Casual","formal":"Formal"},"copyText":"Copy Text","usageTips":"\uD83D\uDCA1 Usage Tips:","usageAdvice":"These templates are for reference only. Please adjust them according to your actual situation and relationship intimacy. Sincere communication is key to building understanding."}},"painTracker":{"title":"Pain Tracker","description":"Track pain patterns, analyze trends, and optimize treatment effectiveness.","cta":"Start Tracking"},"breathingExercise":{"title":"Instant Breathing Exercise Tool","description":"Based on scientifically validated 4-7-8 breathing technique to quickly relieve pain by regulating the nervous system. No download required, start practicing immediately.","usageTips":{"title":"Usage Tips:","bestTiming":{"title":"Best Timing:","items":["When pain just starts","When feeling anxious","Before sleep for relaxation"]},"precautions":{"title":"Precautions:","items":["Find comfortable sitting or lying position","Beginners: 3-4 cycles are enough","Stop if feeling dizzy"]}}}},"scenarioSolutionsPage":{"title":"Scenario-Based Solutions","description":"Professional menstrual pain management strategies tailored for different life situations, helping you stay confident in any environment.","introTitle":"Complete Life Scenario Coverage","introText":"Every woman\'s life is multifaceted, from workplace to home, from exercise to social activities. Different scenarios require different coping strategies. We\'ve carefully prepared comprehensive scenario-based solutions to help you handle menstrual discomfort gracefully, wherever you are.","scenarios":{"office":{"title":"Office/Workplace Scenarios","description":"Discreet relief solutions for professional environments","features":["Meeting Emergency Kit","Office Chair Stretches","Workplace Nutrition"]},"commute":{"title":"Commuting Scenarios","description":"Strategies for sudden pain while traveling","features":["Public Transport Solutions","Driving Adjustments","Emergency Response Plans"]},"exercise":{"title":"Exercise/Outdoor Scenarios","description":"Safety guidelines for physical activities","features":["Period Hiking Guide","Pool Hygiene Management","Yoga Pose Library"]},"sleep":{"title":"Sleep Scenarios","description":"Soothing solutions for nighttime pain","features":["Sleep Audio","Scientific Sleep Positions","Bedtime Nutrition"]},"social":{"title":"Social Scenarios","description":"Graceful management during social activities","features":["Date Emergency Strategies","Party Food Choices","Pain Concealment Techniques"]},"lifeStages":{"title":"Special Life Stages","description":"Specialized solutions for different age groups","features":["Adolescence Programs","Pre-pregnancy Programs","Perimenopause Programs"]}}},"header":{"home":"Home","interactiveSolutions":"Interactive Solutions","articlesDownloads":"Articles & Downloads","scenarioSolutions":"Scenario Solutions","frameworkDemo":"\uD83D\uDE80 Framework Demo","naturalCare":"Natural Care","healthGuide":"Health Guide"},"footer":{"copyright":"\xa9 {currentYear, number, integer} periodhub.health. All rights reserved.","privacy":"Privacy Policy","terms":"Terms of Service","contact_email":"<EMAIL>","disclaimer":"This content is for informational purposes only and should not replace professional medical advice.","linksTitle":"Links","contactTitle":"Contact","medicalDisclaimer":"Medical Disclaimer","articles":"Articles","naturalTherapies":"Natural Therapies","medicalDisclaimerFull":"Medical Disclaimer: The content on this website is for informational and educational purposes only and is not intended to be a substitute for professional medical advice, diagnosis, or treatment. We are not healthcare professionals. Always seek the advice of your physician or other qualified health provider with any questions you may have regarding a medical condition. In case of emergency, seek immediate medical attention. Use of this website does not establish a doctor-patient relationship."},"navigationTabs":{"articles":"\uD83D\uDCDA Articles","pdfDownloads":"\uD83D\uDCE5 PDF Downloads"},"userSuccessStories":{"title":"User Success Stories","statistic":"Over 10,000+ women have found their own solutions here","ctaButton":"Join them and start your healing journey","stories":{"story1":{"name":"Lisa Li","role":"IT Professional, 25","initial":"L","testimonial":"\\"Through personalized assessment, I discovered I have prostaglandin-excess type dysmenorrhea. Following the platform\'s dietary and exercise recommendations, my pain intensity dropped from 8 to 3 points in 3 months, and my work efficiency improved significantly!\\""},"story2":{"name":"Tina Zhang","role":"University Student, 20","initial":"T","testimonial":"\\"The teen section content is so helpful! I learned heat therapy, yoga, and breathing techniques. Now I\'m not afraid of getting my period during exams. I even helped my roommates improve, and our relationships got better!\\""},"story3":{"name":"Wendy Wang","role":"Working Mother, 32","initial":"W","testimonial":"\\"The pain diary feature helped me discover the connection between menstrual pain and stress. Combined with doctor\'s treatment and platform recommendations, I\'ve basically said goodbye to monthly suffering, and my quality of life has improved significantly.\\""}}},"toolsCollectionButton":{"buttonText":"Access Complete Tools Collection"},"searchBox":{"placeholder":"Search articles...","noResults":"No articles found","matchTypes":{"title":"Title","summary":"Summary","tag":"Tag","content":"Content"}},"downloadButton":{"viewDocument":"View Document"},"breathingExercise":{"title":"4-7-8 Breathing Exercise","description":"Based on scientifically validated 4-7-8 breathing method, quickly relieve pain through nervous system regulation. No download required, start practicing immediately.","usageTips":{"title":"Usage Tips:","bestTiming":{"title":"Best Timing:","items":["When pain just starts","When feeling anxious","Before sleep for relaxation"]},"precautions":{"title":"Precautions:","items":["Find comfortable sitting or lying position","Beginners: 3-4 cycles are enough","Stop if feeling dizzy"]}},"subtitle":"Natural pain relief through nervous system regulation","phases":{"inhale":"Inhale","hold":"Hold","exhale":"Exhale"},"instructions":"How to practice:","startButton":"\uD83E\uDEC1 Start Guided Practice","stopButton":"Stop Practice","practiceAgain":"Practice Again","completed":"✅ One cycle completed!","currentPhase":"Current:","benefits":{"title":"Scientific Benefits:","painPerception":"Pain Perception","muscleTension":"Muscle Tension","relaxation":"Relaxation"},"tip":"\uD83D\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.","timeUnit":"s"},"common":{"loading":"Loading...","error":"An error occurred. Please try again.","submit":"Submit","cancel":"Cancel","save":"Save","delete":"Delete","edit":"Edit","view":"View","back":"Back","next":"Next","previous":"Previous","readMore":"Read more","learnMore":"Learn more","seeAll":"See all","tryNow":"Try now","startNow":"Start now","comingSoon":"Coming Soon","importantNote":"Important Note","medicalDisclaimer":"This information is for educational purposes only and is not intended to replace professional medical advice. Always consult with a healthcare provider for medical concerns."},"healthGuidePage":{"title":"Comprehensive Menstrual Health Guide","description":"Your complete menstrual health resource, from basics to advanced management strategies, helping you understand and manage your menstrual health comprehensively."},"teenHealth":{"title":"Teen Menstrual Health Zone","description":"Specialized menstrual health guidance designed for teenagers, covering physical development, emotional support, and campus life management.","campusGuide":{"title":"Campus Life Guide","description":"Practical advice and strategies for managing periods in school environments."},"developmentPain":{"title":"Developmental Pain Management","description":"Understanding the characteristics and management of menstrual pain during adolescence."},"emotionalSupport":{"title":"Emotional Support","description":"Mental health guidance for coping with period-related emotional changes."},"communicationGuide":{"title":"Communication Guide","description":"How to talk about periods with parents, teachers, and friends."}},"toolPage":{"backToTools":"Back to Tools","toolNotFound":"Tool Not Found","toolNotFoundDesc":"Sorry, we could not find the tool you are looking for.","medicalDisclaimer":"Medical Disclaimer","medicalDisclaimerText":"This tool is for reference only and cannot replace professional medical advice, diagnosis, or treatment. Please consult healthcare professionals for any health concerns.","categories":{"assessment":"Assessment","tracking":"Tracking","constitutionAssessment":"Constitution Assessment","healthAssessment":"Health Assessment"},"difficulty":{"easy":"Easy"},"estimatedTime":{"5to10min":"5-10 minutes","2to3minDaily":"2-3 minutes daily","5to8min":"5-8 minutes","3to5min":"3-5 minutes"}},"downloads":{"common":{"download":"View Document","document":"Document","pages":"pages"}},"painTracker":{"title":"Pain Tracker","subtitle":"Record and track your menstrual pain to understand patterns and find the most effective relief methods","description":"Help manage your menstrual health by keeping detailed pain records","navigation":{"overview":"Overview","addEntry":"Add Entry","viewEntries":"View Entries","statistics":"Statistics","export":"Export Data"},"form":{"title":"Pain Record","editTitle":"Edit Pain Record","date":"Date","painLevel":"Pain Level","duration":"Duration","location":"Pain Location","menstrualStatus":"Menstrual Status","symptoms":"Symptoms","remedies":"Remedies Used","effectiveness":"Effectiveness Rating","notes":"Notes","optional":"optional","minutes":"minutes","save":"Save Record","cancel":"Cancel","saving":"Saving...","edit":"Edit Record","update":"Update Record","updating":"Updating...","notesPlaceholder":"Record any additional information such as triggers, mood, etc..."},"entries":{"title":"Pain Records","noEntries":"No pain records yet","noEntriesDescription":"Start recording your pain information to better understand pain patterns","addFirst":"Add First Record","totalEntries":"Total Records","lastEntry":"Last Entry","edit":"Edit","delete":"Delete","confirmDelete":"Confirm Delete","deleteMessage":"Are you sure you want to delete this record? This action cannot be undone.","deleteSuccess":"Record deleted successfully","deleteError":"Failed to delete record, please try again","painIntensity":"Pain Intensity","duration":"Duration","minutes":"minutes"},"statistics":{"title":"Statistics","overview":"Overview","trends":"Trend Analysis","patterns":"Pain Patterns","remedies":"Remedy Effectiveness","totalEntries":"Total Records","averagePain":"Average Pain Level","maxPain":"Maximum Pain Level","minPain":"Minimum Pain Level","trendDirection":"Pain Trend","improving":"Improving","worsening":"Worsening","stable":"Stable","mostCommonSymptoms":"Most Common Symptoms","mostEffectiveRemedies":"Most Effective Remedies","painFrequency":"Pain Level Distribution","noData":"No data available","noDataDescription":"Add more records to view statistical analysis","painLevelChart":"Pain Level Trends","cyclePatterns":"Menstrual Cycle Pain Patterns","remedyEffectiveness":"Remedy Effectiveness","inDevelopment":"Statistics feature is under development..."},"export":{"title":"Export Data","description":"Export your pain records as files for backup or sharing with healthcare providers","format":"Export Format","json":"JSON Format","csv":"CSV Format","pdf":"PDF Report","dateRange":"Date Range","allData":"All Data","lastMonth":"Last Month","lastThreeMonths":"Last 3 Months","lastSixMonths":"Last 6 Months","customRange":"Custom Range","startDate":"Start Date","endDate":"End Date","includeCharts":"Include Charts","includeStatistics":"Include Statistics","export":"Export","exporting":"Exporting...","exportSuccess":"Export successful","exportError":"Export failed, please try again","inDevelopment":"Export feature is under development..."},"insights":{"title":"Health Insights","painPatterns":"Pain Pattern Analysis","recommendations":"Personalized Recommendations","trends":"Trend Analysis","alerts":"Health Alerts","noInsights":"No insights available","noInsightsDescription":"Record more data to get personalized health insights"},"settings":{"title":"Settings","notifications":"Notification Settings","reminders":"Recording Reminders","privacy":"Privacy Settings","dataManagement":"Data Management","clearAllData":"Clear All Data","clearDataWarning":"This action will permanently delete all pain records and cannot be undone","clearDataConfirm":"Confirm Clear","clearDataSuccess":"Data cleared successfully","clearDataError":"Failed to clear data, please try again"},"messages":{"saveSuccess":"Record saved successfully","saveError":"Failed to save record, please try again","updateSuccess":"Record updated successfully","updateError":"Failed to update record, please try again","deleteSuccess":"Record deleted successfully","deleteError":"Failed to delete record, please try again","confirmDelete":"Are you sure you want to delete this record? This action cannot be undone.","loadError":"Failed to load data, please refresh the page","networkError":"Network error, please check your connection","validationError":"Please check your input","duplicateDate":"A record for this date already exists, please choose another date or edit the existing record","assessmentComplete":"Assessment Complete","assessmentCompleteDesc":"Your personalized recommendations have been generated","assessmentFailed":"Assessment Failed","assessmentFailedDesc":"Unable to generate assessment results, please try again","resultsSaved":"Results Saved","resultsSavedDesc":"You can view your assessment results anytime","close":"Close"},"help":{"title":"Help","gettingStarted":"Getting Started","recordingPain":"Recording Pain","viewingData":"Viewing Data","exportingData":"Exporting Data","tips":"Tips","faq":"FAQ"},"assessment":{"title":"Symptom Assessment Tool","subtitle":"Quickly identify pain types through professional questionnaires and receive precise personalized recommendations.","start":{"title":"Before Starting","description":"Please ensure you\'re in a quiet, private environment where you can focus on answering the questions. This assessment will help you better understand your symptom patterns.","feature1":"Professional symptom analysis","feature2":"Personalized recommendations","feature3":"Scientific assessment methods","feature4":"Instant result feedback","startButton":"Start Assessment","disclaimer":"This assessment is for reference only and cannot replace professional medical diagnosis."},"progress":{"questionOf":"Question {current} of {total}"},"navigation":{"previous":"Previous","next":"Next","skip":"Skip","finish":"Complete Assessment"},"result":{"title":"Assessment Results","yourScore":"Your Score","severity":"Severity","summary":"Summary","recommendations":"Recommendations","timeframe":"Timeframe:","actionSteps":"Action Steps","retakeAssessment":"Retake Assessment","saveResults":"Save Results","nextSteps":{"trackSymptoms":"Use pain tracker to record symptoms","tryRecommendations":"Try recommended relief methods","consultDoctor":"If symptoms persist or worsen, consult a doctor"}},"severity":{"mild":"Mild","moderate":"Moderate","severe":"Severe","emergency":"Emergency"},"priority":{"high":"High Priority","medium":"Medium Priority","low":"Low Priority"},"messages":{"assessmentComplete":"Assessment Complete","assessmentCompleteDesc":"Your symptom assessment is complete. Please review the results and recommendations.","assessmentFailed":"Assessment Failed","assessmentFailedDesc":"An error occurred during assessment. Please try again.","resultsSaved":"Results Saved","resultsSavedDesc":"Your assessment results have been saved to local storage."},"resultMessages":{"emergency":"Your symptoms are quite severe. We recommend consulting a healthcare professional as soon as possible.","emergencySummary":"Assessment indicates you may need professional medical attention.","severe":"Your symptoms are quite serious. We recommend adopting comprehensive management strategies.","severeSummary":"Your symptoms require active management and possible medical intervention.","moderate":"You have moderate symptoms that can be managed through various methods.","moderateSummary":"Your symptoms are manageable with recommended relief strategies.","mild":"Your symptoms are relatively mild and can be well managed through simple self-care.","mildSummary":"Your symptoms are mild and can be improved through lifestyle adjustments."},"recommendations":{"emergencyMedical":{"title":"Seek Immediate Medical Care","description":"Your symptoms may require professional medical evaluation and treatment","timeframe":"Immediately","actionSteps":["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]},"painManagement":{"title":"Pain Management Strategies","description":"Multiple methods can help relieve menstrual pain","timeframe":"Immediately available","actionSteps":["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]},"lifestyleChanges":{"title":"Lifestyle Adjustments","description":"Long-term lifestyle changes can significantly improve symptoms","timeframe":"2-3 months to see effects","actionSteps":["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]},"selfcarePractices":{"title":"Self-Care Practices","description":"Daily self-care can help you better manage symptoms","timeframe":"Ongoing","actionSteps":["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]}}}},"homePageContent":{"searchPlaceholder":"\uD83D\uDD0D Quick search for pain relief solutions...","searchTips":"\uD83D\uDCA1 Try searching \\"5-minute relief\\", \\"heat therapy\\", \\"prostaglandins\\"","statistics":{"title":"Data-Driven Results","description":"Statistics based on real user feedback and scientific research","improvement":"Users Report Improvement","users":"100K+","totalUsers":"Total Users","support":"Online Support","articles":"Expert Articles"},"healthGuide":{"title":"Health Guide","description":"Comprehensive menstrual health knowledge system, from basic understanding to advanced management strategies."},"smartTools":{"title":"Smart Health Tools","description":"Professional assessment and tracking tools to help you better understand and manage your menstrual health"},"medicalDisclaimer":"Medical Disclaimer"}}')},4874:e=>{"use strict";e.exports=JSON.parse('{"site":{"name":"periodhub.health","title":"periodhub.health - 您的经期健康指南","description":"您的贴心伙伴，提供有效方案与支持资源，助您轻松应对痛经困扰，走向更健康的生理周期。"},"navigation":{"home":"首页","articles":"文章PDF下载中心","therapies":"疗法","instantRelief":"即时缓解方案","naturalTherapies":"平时调理","culturalCharms":"文化慰藉（符咒）","scenarioSolutions":"场景解决方案","downloads":"PDF下载","interactiveTools":"互动解决方案","symptomAssessment":"症状评估","painTracker":"疼痛追踪","healthGuide":"痛经健康指南"},"homepage":{"hero":{"headline":"告别经期不适，掌控生理周期。","subheadline":"您的贴心伙伴，提供有效方案与支持资源，助您轻松应对痛经困扰。","bodyCopy":"欢迎来到 periodhub.health – 您值得信赖的经期健康伙伴。我们提供循证的见解、实用的工具和贴心的支持，帮助您理解、管理并缓解经期不适。无论您需要快速的缓解方法、长期的策略，还是对身体更深入的了解，我们都在这里为您服务。","ctaExplore":"探索方案","ctaCheckSymptoms":"开始症状检查","imageAlt":"一位女性在温馨的环境中表达关怀和理解的形象","ctaDiscoverNatural":"发现自然疗法"},"modules":{"title":"发现您的舒适之道","instantRelief":{"title":"即时缓解","description":"在您最需要的时候，快速缓解痛经的技巧和方法。找到现在就可以使用的即时舒适方法。"},"interactiveSolutions":{"title":"互动解决方案","description":"引人入胜的工具和个性化见解，助您主动管理月经健康。使用我们的症状检查器和资源获取针对性指导。"},"dailyConditioning":{"title":"平时调理","description":"通过调整生活方式和日常习惯，侧重自然方法，建立对月经不适的长期抵抗力，并支持整体健康。"}},"featuredContent":{"title":"精选文章","viewAll":"查看所有文章","readMore":"阅读更多"}},"articlesPage":{"title":"文章与指南","description":"浏览我们的文章合集，了解更多关于月经健康和疼痛缓解的知识。"},"articleList":{"publishedOn":"发布于 {date, date, long}","readMore":"阅读更多","noArticles":"暂无文章。请稍后再回来查看！"},"articleDetail":{"backToList":"返回文章列表","publishedOn":"发布于 {date, date, long}","notFoundTitle":"文章未找到","notFoundDescription":"抱歉，我们找不到您要查找的文章。"},"therapiesPage":{"title":"缓解疗法","description":"探索各种疗法和方法，以管理和减轻经期疼痛。"},"therapyList":{"lastUpdated":"最后更新于 {date, date, long}","learnMore":"了解更多","noTherapies":"暂无疗法。请稍后再回来查看！"},"therapyDetail":{"backToList":"返回疗法列表","lastUpdated":"最后更新于 {date, date, long}","notFoundTitle":"疗法未找到","notFoundDescription":"抱歉，我们找不到您要查找的疗法。"},"SpecialTherapiesPage":{"title":"特殊疗法","description":"探索传统和现代的特殊疗法，为您的经期健康提供更多选择。","introduction":"特殊疗法结合了传统智慧和现代科学，为经期不适提供独特的缓解方法。这些疗法注重整体健康，帮助您找到最适合自己的治疗方式。","therapyCards":{"title":"精选疗法","viewAll":"查看全部"},"videoSection":{"title":"疗法演示视频","description":"通过专业视频演示，学习正确的疗法技巧和注意事项。"},"disclaimer":"此处提供的疗法信息仅供参考，不能替代专业医疗建议。在尝试任何新的治疗方法之前，请咨询医疗专业人士。"},"immediateReliefPage":{"title":"经期疼痛即时缓解方案","description":"探索在您最需要时快速有效缓解痛经的方法。了解您可以立即尝试的技巧。","introTitle":"当经期疼痛来袭时...","introText":"当经期疼痛来袭时，您需要快速有效的方法来获得舒适。本节致力于提供即时缓解方案——您可以立即采用的方法，以帮助缓解急性经期痉挛和不适。","typesTitle":"即时缓解类型","heatTherapy":"热疗","gentleMovement":"温和运动与呼吸","acupressure":"穴位按摩","otcOptions":"非处方药选项","findingWhatWorksTitle":"找到适合您的方法","findingWhatWorksText":"找到最适合您身体和疼痛类型的即时缓解方法，关键在于尝试。考虑结合不同的方法以增强舒适度。","contentSectionTitle":"相关文章与疗法"},"naturalTherapiesPage":{"title":"经期健康自然疗法与平时调理","description":"发现温和的自然方法和生活方式调整，以实现长期的月经健康并减轻疼痛。","introTitle":"培养长期健康","introText":"除了即时缓解，培养长期的经期健康还包括将自然疗法和有意识的日常习惯融入您的生活。本节探讨了有助于随着时间推移减少经期疼痛的频率和严重程度，并支持您整个生理周期整体健康的整体方法。","holisticApproachesTitle":"长期舒适的整体方法","dietNutrition":"饮食与营养","herbalRemedies":"草药与补充剂","movementExercise":"运动与锻炼","mindBodyPractices":"身心练习","traditionalPractices":"传统习俗","consistencyIsKeyTitle":"持之以恒是关键","consistencyIsKeyText":"整合这些自然方法需要时间和坚持。请对自己保持耐心，并专注于培养可持续的习惯，以支持您整个生理周期（而不仅仅是经期）的身体健康。","contentSectionTitle":"相关疗法与指南"},"culturalCharmsPage":{"title":"探索文化中的慰藉：符咒与传统疗愈符号","description":"探索符咒、符号和传统习俗在历史和文化中用于寻求舒适和精神支持的意义。","introduction":"纵观历史，在不同的文化中，人们在脆弱时期，包括经期，都会求助于符号、物品和传统习俗来寻求舒适、保护和支持。这些习俗深深植根于文化信仰体系和民间传说。","introTitle":"文化传统的旅程","introText":"纵观历史，在不同的文化中，人们在脆弱时期，包括经期，都会求助于符号、物品和传统习俗来寻求舒适、保护和支持。这些习俗深深植根于文化信仰体系和民间传说。","understandingTitle":"理解文化疗愈传统","understandingText":"许多文化都有涉及使用特定物品或执行某些仪式的传统，这些仪式被认为可以影响健康、带来好运或驱除负能量。对一些人来说，这延伸到在经期不适等身体挑战期间寻求舒适或缓解。","multimediaTitle":"文化符号与意象","contentSection1Description":"探索各种文化中用于舒适和精神支持的传统符号和物品。","disclaimerTitle":"文化支持免责声明","disclaimer":"此处提供的有关文化符咒、传统习俗和相关信仰的信息仅供文化和信息目的。这些习俗植根于传统和信仰体系，不应被视为，也不应被解释为专业医疗建议、诊断或治疗的替代品。","disclaimerText":"此处提供的有关文化符咒、传统习俗和相关信仰的信息仅供文化和信息目的。这些习俗植根于传统和信仰体系，不应被视为，也不应被解释为专业医疗建议、诊断或治疗的替代品。","mediaAreaTitle":"文化符号与意象","imagePlaceholder":"图片/媒体占位符","promptSuggestionPrefix":"AI 提示: ","backHome":"返回首页"},"interactiveToolsPage":{"title":"经期健康管理互动工具","description":"使用我们的互动工具，更好地了解您的症状，追踪您的疼痛模式，并获得个性化建议。","symptomAssessment":{"title":"症状评估工具","description":"回答几个关于您症状的问题，以获得管理经期疼痛的个性化建议。","startButton":"开始评估","questions":{"painLocation":"您的经期疼痛部位在哪里？","painIntensity":"您如何评价您的疼痛强度？","painDuration":"您的疼痛通常持续多久？","accompaniedSymptoms":"您的经期疼痛伴随哪些症状？","reliefMethods":"您尝试过哪些缓解方法？"},"results":{"title":"您的个性化建议","disclaimer":"这些建议基于您的回答，仅供参考。它们不能替代专业医疗建议。","tryAgain":"重新评估","saveResults":"保存结果"}},"painTracker":{"title":"疼痛追踪系统","description":"记录和监测您的经期疼痛模式，以识别触发因素和有效的缓解方法。","startButton":"开始追踪","addEntry":"添加新记录","viewHistory":"查看历史","entryForm":{"date":"日期","painLevel":"疼痛等级","location":"疼痛位置","symptoms":"相关症状","remedies":"使用的缓解方法","effectiveness":"有效性","notes":"备注","save":"保存记录","cancel":"取消"},"insights":{"title":"您的疼痛洞察","description":"根据您的追踪历史，以下是我们注意到的一些模式：","noData":"数据还不够。继续追踪以查看洞察。"},"assessment":{"title":"症状评估工具","subtitle":"通过专业问卷快速识别疼痛类型，为您提供精准的个性化建议。","start":{"title":"开始评估前","description":"请确保您在一个安静、私密的环境中，可以专心回答问题。这个评估将帮助您更好地了解自己的症状模式。","feature1":"专业的症状分析","feature2":"个性化建议","feature3":"科学的评估方法","feature4":"即时结果反馈","startButton":"开始评估","disclaimer":"此评估仅供参考，不能替代专业医疗诊断。"},"progress":{"questionOf":"第 {current} 题，共 {total} 题"},"navigation":{"previous":"上一题","next":"下一题","skip":"跳过","finish":"完成评估"},"result":{"title":"评估结果","yourScore":"您的得分","severity":"严重程度","summary":"结果摘要","recommendations":"建议方案","timeframe":"时间框架：","actionSteps":"行动步骤","retakeAssessment":"重新评估","saveResults":"保存结果","nextSteps":{"trackSymptoms":"使用疼痛追踪器记录症状","tryRecommendations":"尝试推荐的缓解方法","consultDoctor":"如果症状持续或恶化，请咨询医生"}},"severity":{"mild":"轻度","moderate":"中度","severe":"重度","emergency":"紧急"},"priority":{"high":"高优先级","medium":"中优先级","low":"低优先级"},"messages":{"assessmentComplete":"评估完成","assessmentCompleteDesc":"您的症状评估已完成，请查看结果和建议。","assessmentFailed":"评估失败","assessmentFailedDesc":"评估过程中出现错误，请重试。","resultsSaved":"结果已保存","resultsSavedDesc":"您的评估结果已保存到本地存储。"},"resultMessages":{"emergency":"您的症状较为严重，建议尽快咨询医疗专业人士。","emergencySummary":"评估显示您可能需要专业医疗关注。","severe":"您的症状比较严重，建议采取综合管理策略。","severeSummary":"您的症状需要积极的管理和可能的医疗干预。","moderate":"您有中等程度的症状，可以通过多种方法进行管理。","moderateSummary":"您的症状是可以管理的，建议采用多种缓解策略。","mild":"您的症状相对较轻，通过简单的自我护理就能很好地管理。","mildSummary":"您的症状较轻，可以通过生活方式调整来改善。"},"recommendations":{"emergencyMedical":{"title":"建议立即就医","description":"您的症状可能需要专业医疗评估和治疗","timeframe":"立即","actionSteps":["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"]},"painManagement":{"title":"疼痛管理策略","description":"多种方法可以帮助缓解经期疼痛","timeframe":"立即可用","actionSteps":["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"]},"lifestyleChanges":{"title":"生活方式调整","description":"长期的生活方式改变可以显著改善症状","timeframe":"2-3个月见效","actionSteps":["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"]},"selfcarePractices":{"title":"自我护理实践","description":"日常的自我护理可以帮助您更好地管理症状","timeframe":"持续进行","actionSteps":["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"]}}}},"embeddedPainAssessment":{"title":"\uD83D\uDCA1 痛经快速自测","subtitle":"1分钟了解您的痛经程度，获得初步建议","question":"您的痛经强度如何？","selectIntensityFirst":"请先选择痛经强度","options":{"mild":"轻微（可以忍受，不影响日常活动）","moderate":"中度（影响部分活动，但能坚持）","severe":"重度（完全影响日常活动，需要休息）"},"buttons":{"getAdvice":"获取建议","detailedAssessment":"详细评估","testAgain":"重新测试","fullAssessment":"完整评估"},"resultTitle":"评估结果","results":{"mild":"您的痛经程度较轻，可以尝试热敷、轻度运动等自然缓解方法。","moderate":"您的痛经程度中等，建议结合多种缓解方法，如有需要可考虑非处方止痛药。","severe":"您的痛经程度较重，建议咨询医生获得专业评估和治疗建议。"},"disclaimer":"⚠️ 此工具仅供参考，不能替代专业医疗建议"},"searchBox":{"placeholder":"搜索文章...","matchTypes":{"title":"标题","summary":"摘要","tag":"标签","content":"内容"},"noResults":"未找到相关文章"},"breathingExercise":{"title":"即时呼吸练习工具","description":"基于科学验证的4-7-8呼吸法，通过调节神经系统快速缓解疼痛。无需下载，立即开始练习。","usageTips":{"title":"使用建议：","bestTiming":{"title":"最佳时机：","items":["疼痛刚开始时","感到焦虑紧张时","睡前放松时"]},"precautions":{"title":"注意事项：","items":["找舒适的坐位或躺位","初学者3-4个循环即可","如感到头晕请停止练习"]}},"subtitle":"[ZH] Natural pain relief through nervous system regulation","phases":{"inhale":"[ZH] Inhale","hold":"[ZH] Hold","exhale":"[ZH] Exhale"},"instructions":"[ZH] How to practice:","startButton":"[ZH] \uD83E\uDEC1 Start Guided Practice","stopButton":"[ZH] Stop Practice","practiceAgain":"[ZH] Practice Again","completed":"[ZH] ✅ One cycle completed!","currentPhase":"[ZH] Current:","benefits":{"title":"[ZH] Scientific Benefits:","painPerception":"[ZH] Pain Perception","muscleTension":"[ZH] Muscle Tension","relaxation":"[ZH] Relaxation"},"tip":"[ZH] \uD83D\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.","timeUnit":"[ZH] s"},"painDifferentialDiagnosis":{"title":"疼痛鉴别诊断","description":"通过专业问诊帮助您识别疼痛类型，判断是否需要就医。","startButton":"开始诊断"},"personalizedInsights":{"title":"个性化洞察（即将推出）","description":"基于您的追踪数据和评估结果，深入了解您的经期健康模式。（即将推出）"},"periodPainAssessment":{"title":"痛经速测小工具","description":"回答几个简单问题，初步了解你的痛经类型和严重程度，获得个性化的健康建议。","cta":"立即评估","tool":{"title":"痛经速测小工具","subtitle":"回答几个简单问题，初步了解你的痛经类型和严重程度。","intensityQuestion":"你的痛经强度如何?","intensityOptions":{"mild":"轻微（可以忍受，不影响日常活动）","moderate":"中度（影响部分活动，但能坚持）","severe":"重度（完全影响日常活动，需要休息）"},"onsetQuestion":"你的痛经何时开始?","onsetOptions":{"recent":"初潮后不久（1-2年内）","later":"较晚开始（初潮多年后）"},"symptomsQuestion":"你有以下任何症状吗？（可多选）","symptoms":{"fever":"发热（体温超过38\xb0C）","vomiting":"严重呕吐","dizziness":"昏厥或头晕目眩","bleeding":"异常出血（大量或血块）","nonMenstrual":"非经期时也有盆腔痛"},"assessButton":"评估是否需要就医","resetButton":"重新评估","moreInfoButton":"查看更多青少年健康指南","disclaimer":"⚠️ 本工具仅供参考，不能替代专业医疗诊断。如有疑虑，请咨询医生。","medicalDisclaimer":"医疗免责声明","result":{"resultTitle":"评估结果","consultAdvice":"建议您尽快咨询医生或妇科专家，以获得专业的医疗建议和治疗方案。","validationMessage":"请先选择痛经强度和开始时间","assessments":{"severe_symptoms":"根据您选择的症状（如发热、剧烈呕吐、昏厥或异常出血），建议您尽快咨询医生，这些可能是需要医疗评估的警示信号。","severe_late":"重度痛经如果在初潮较晚后开始，可能提示继发性痛经的风险，建议咨询医生排除潜在的妇科问题。","severe_early":"您的痛经严重程度较高。虽然可能是原发性痛经，但影响日常生活的严重疼痛建议医学评估，以获得有效的疼痛管理方案。","moderate_late":"中度痛经在初潮较晚后开始，建议咨询医生排除继发性痛经的可能性。","normal":"基于您提供的信息，您的痛经情况属于常见范围，可能是原发性痛经。建议尝试自然缓解方法，如热敷、轻度运动和健康饮食习惯等。如症状加重或出现异常，请及时咨询医生。"}}}},"constitutionTest":{"title":"中医体质测试","description":"通过8个问题了解您的中医体质类型，获得个性化的穴位、饮食和生活方式建议","cta":"开始测试"},"toolsIntroduction":"我们的互动工具旨在为您提供知识和个性化策略，帮助您管理经期健康。通过了解您独特的症状和模式，您可以做出明智的决定并找到更有效的缓解方法。","developmentNote":"我们正在不断开发新的工具和资源。请经常回来查看更新！"},"interactiveTools":{"common":{"loading":"加载中...","error":"发生错误，请重试","submit":"提交","cancel":"取消","save":"保存","reset":"重置","back":"返回","next":"下一步","previous":"上一步","close":"关闭","startNow":"立即开始","tryNow":"立即尝试","learnMore":"了解更多"},"navigation":{"backToTools":"返回工具列表","toolsTitle":"互动工具","toolsDescription":"专业的经期健康管理工具，帮助您更好地了解和管理自己的健康状况"},"categories":{"assessment":"评估工具","tracking":"追踪工具","constitutionAssessment":"体质评估","healthAssessment":"健康评估"},"difficulty":{"easy":"简单","medium":"中等","hard":"困难"},"estimatedTime":{"5to10min":"5-10分钟","2to3minDaily":"每日2-3分钟","5to8min":"5-8分钟","3to5min":"3-5分钟"},"medicalDisclaimer":{"title":"医疗免责声明","text":"此工具仅供参考，不能替代专业医疗建议、诊断或治疗。如有健康问题，请咨询医疗专业人士。","shortText":"⚠️ 本工具仅供参考，不能替代专业医疗诊断。如有疑虑，请咨询医生。"},"periodPainAssessment":{"title":"痛经速测小工具","description":"回答几个简单问题，初步了解你的痛经类型和严重程度，获得个性化的健康建议。","cta":"立即评估","subtitle":"回答几个简单问题，初步了解你的痛经类型和严重程度。","questions":{"intensity":{"title":"你的痛经强度如何?","options":{"mild":"轻微（可以忍受，不影响日常活动）","moderate":"中度（影响部分活动，但能坚持）","severe":"重度（完全影响日常活动，需要休息）"}},"onset":{"title":"你的痛经何时开始?","options":{"recent":"初潮后不久（1-2年内）","later":"较晚开始（初潮多年后）"}},"symptoms":{"title":"你有以下任何症状吗？（可多选）","options":{"fever":"发热（体温超过38\xb0C）","vomiting":"严重呕吐","dizziness":"昏厥或头晕目眩","bleeding":"异常出血（大量或血块）","nonMenstrual":"非经期时也有盆腔痛"}}},"actions":{"assess":"评估是否需要就医","reset":"重新评估","moreInfo":"查看更多青少年健康指南"},"results":{"title":"评估结果","consultAdvice":"建议您尽快咨询医生或妇科专家，以获得专业的医疗建议和治疗方案。","validationMessage":"请先选择痛经强度和开始时间","assessments":{"severe_symptoms":"根据您选择的症状（如发热、剧烈呕吐、昏厥或异常出血），建议您尽快咨询医生，这些可能是需要医疗评估的警示信号。","severe_late":"重度痛经如果在初潮较晚后开始，可能提示继发性痛经的风险，建议咨询医生排除潜在的妇科问题。","severe_early":"您的痛经严重程度较高。虽然可能是原发性痛经，但影响日常生活的严重疼痛建议医学评估，以获得有效的疼痛管理方案。","moderate_late":"中度痛经在初潮较晚后开始，建议咨询医生排除继发性痛经的可能性。","normal":"基于您提供的信息，您的痛经情况属于常见范围，可能是原发性痛经。建议尝试自然缓解方法，如热敷、轻度运动和健康饮食习惯等。如症状加重或出现异常，请及时咨询医生。"}}},"constitutionTest":{"title":"中医体质测试","description":"通过8个问题了解您的中医体质类型，获得个性化的穴位、饮食和生活方式建议","cta":"开始测试","subtitle":"通过专业的中医体质问卷，了解您的体质类型，获得个性化的健康建议","features":{"quick":{"title":"快速便捷","description":"仅需5-8分钟完成"},"professional":{"title":"专业可靠","description":"基于中医理论设计"},"personalized":{"title":"个性化建议","description":"针对您的体质特点"},"practical":{"title":"实用指导","description":"穴位、饮食、生活建议"}},"instructions":{"title":"测试说明","item1":"请根据您最近3个月的身体状况回答","item2":"选择最符合您情况的选项","item3":"如有疑问，选择相对更符合的选项","item4":"测试结果仅供参考，不替代医疗诊断"},"navigation":{"startTest":"开始测试","nextQuestion":"下一题","previousQuestion":"上一题","completeTest":"完成测试","retakeTest":"重新测试","previous":"上一题","next":"下一题"},"painScale":{"title":"疼痛程度：","reference":"疼痛程度参考","levels":{"none":"无痛","mild":"轻微","moderate":"中度","severe":"严重","extreme":"极重"},"descriptions":{"0-2":"无痛或轻微不适","3-4":"轻度疼痛，可忍受","5-7":"中度疼痛，影响活动","8-10":"重度疼痛，难以忍受"}},"progress":{"questionOf":"第 {current} 题，共 {total} 题","complete":"完成"},"result":{"title":"测试结果","subtitle":"您的中医体质类型分析","match":"匹配度","constitutionFeatures":"体质特征","commonSymptoms":"常见症状","menstrualFeatures":"月经特点"},"recommendations":{"acupoints":{"title":"穴位调理建议","primaryAcupoints":"主要穴位","location":"位置：","function":"功效：","method":"方法：","guidelines":"按摩指导","technique":"手法：","frequency":"频率：","duration":"时长："},"dietary":{"title":"饮食调理建议","beneficialFoods":"适宜食物","foodsToAvoid":"避免食物","dietaryPrinciples":"饮食原则"},"lifestyle":{"title":"场景化生活建议","description":"根据您的体质特点，为不同生活场景提供个性化建议","reminder":"温馨提醒：","reminderText":"以上建议需要持续实践才能看到效果，建议结合专业医师指导。"},"menstrualPain":{"title":"痛经专项建议","acupointTherapy":"穴位疗法","lifestyleAdjustments":"生活调理"}},"messages":{"testComplete":"测试完成","testCompleteDesc":"您的个性化体质分析已生成","testFailed":"测试失败","testFailedDesc":"无法生成测试结果，请重试"},"emergencyKit":{"title":"个性化应急包推荐","description":"根据您的体质特点，为您推荐专属的应急包物品清单。提前准备，让经期更从容。","priority":{"high":"必需","medium":"推荐","low":"可选"},"packingTips":"\uD83D\uDCE6 打包建议：","packingAdvice":"优先携带\\"必需\\"物品，根据外出时间和场景选择\\"推荐\\"和\\"可选\\"物品。建议准备一个专用的小包，方便随时取用。"},"articles":{"title":"为您推荐的健康文章","readMore":"阅读全文"},"communication":{"title":"沟通模板助手","description":"经期不适时，与身边的人沟通很重要。这些模板可以帮助你更好地表达需求和寻求理解。","styles":{"intimate":"亲密","casual":"随意","formal":"正式"},"copyText":"复制文本","usageTips":"\uD83D\uDCA1 使用提示：","usageAdvice":"这些模板仅供参考，请根据你的实际情况和关系亲密度进行调整。真诚的沟通是建立理解的关键。"}},"painTracker":{"title":"疼痛追踪器","description":"记录疼痛模式，分析趋势变化，优化治疗效果。","cta":"开始追踪"},"breathingExercise":{"title":"即时呼吸练习工具","description":"基于科学验证的4-7-8呼吸法，通过调节神经系统快速缓解疼痛。无需下载，立即开始练习。","usageTips":{"title":"使用建议：","bestTiming":{"title":"最佳时机：","items":["疼痛刚开始时","感到焦虑紧张时","睡前放松时"]},"precautions":{"title":"注意事项：","items":["找舒适的坐位或躺位","初学者3-4个循环即可","如感到头晕请停止练习"]}}}},"scenarioSolutionsPage":{"title":"场景化解决方案","description":"针对不同生活场景的专业痛经应对方案，让您在任何环境下都能从容应对。","introTitle":"生活场景全覆盖","introText":"每个女性的生活都是多元化的，从职场到家庭，从运动到社交，不同的场景需要不同的应对策略。我们为您精心准备了全方位的场景化解决方案，让您无论身处何地，都能优雅从容地应对痛经困扰。","scenarios":{"office":{"title":"办公/职场场景","description":"专业环境下的隐蔽缓解方案","features":["会议应急包","办公椅拉伸","职场饮食管理"]},"commute":{"title":"通勤场景","description":"路上突发疼痛的应对策略","features":["公共交通应对","自驾调整","应急处理方案"]},"exercise":{"title":"运动/户外场景","description":"运动中的安全防护指南","features":["经期徒步指南","泳池卫生管理","瑜伽体式库"]},"sleep":{"title":"睡眠场景","description":"夜间疼痛的舒缓方案","features":["助眠音频","科学睡姿","睡前饮食建议"]},"social":{"title":"社交场景","description":"社交活动中的优雅应对","features":["约会应急策略","聚会饮食选择","疼痛伪装技巧"]},"lifeStages":{"title":"特殊生理阶段","description":"不同年龄段的专项方案","features":["青春期专项","备孕期专项","围绝经期专项"]}}},"header":{"home":"首页","interactiveSolutions":"互动解决方案","articlesDownloads":"文章PDF下载中心","scenarioSolutions":"场景解决方案","frameworkDemo":"\uD83D\uDE80 框架演示","naturalCare":"平时调理","healthGuide":"痛经健康指南"},"footer":{"copyright":"\xa9 {currentYear, number, integer} periodhub.health。保留所有权利。","privacy":"隐私政策","terms":"服务条款","contact_email":"<EMAIL>","disclaimer":"此内容仅供参考，不能替代专业医疗建议。","linksTitle":"链接","contactTitle":"联系我们","medicalDisclaimer":"医疗免责声明","articles":"文章PDF下载中心","naturalTherapies":"平时调理","medicalDisclaimerFull":"医疗免责声明：本网站内容仅供信息和教育目的，不能替代专业医疗建议、诊断或治疗。我们不是医疗专业人员。如有任何医疗问题，请咨询您的医生或其他合格的医疗服务提供者。紧急情况下，请立即寻求医疗救助。使用本网站不构成医患关系。"},"navigationTabs":{"articles":"\uD83D\uDCDA 专业文章","pdfDownloads":"\uD83D\uDCE5 PDF下载"},"userSuccessStories":{"title":"用户成功案例","statistic":"已有超过10,000+女性在这里找到了属于自己的解决方案","ctaButton":"加入她们，开始您的康复之旅","stories":{"story1":{"name":"李小雅","role":"IT从业者，25岁","initial":"李","testimonial":"\\"通过个性化评估发现我属于前列腺素过度分泌型痛经，按照平台建议调整饮食和运动，3个月后疼痛强度从8分降到3分，工作效率大幅提升！\\""},"story2":{"name":"张婷婷","role":"大学生，20岁","initial":"张","testimonial":"\\"青少年专区的内容太有用了！学会了热敷、瑜伽和呼吸法，现在考试期间来大姨妈也不怕了。还帮助室友一起改善，大家感情更好了。\\""},"story3":{"name":"王芳","role":"职场妈妈，32岁","initial":"王","testimonial":"\\"疼痛日志功能帮我发现了痛经与压力的关联性。配合医生治疗使用平台建议，现在基本告别了每月的痛苦，生活质量改善明显。\\""}}},"toolsCollectionButton":{"buttonText":"访问完整工具集页面"},"searchBox":{"placeholder":"搜索文章...","noResults":"未找到相关文章","matchTypes":{"title":"标题","summary":"摘要","tag":"标签","content":"内容"}},"downloadButton":{"viewDocument":"查看文档"},"common":{"loading":"加载中...","error":"发生错误。请重试。","submit":"提交","cancel":"取消","save":"保存","delete":"删除","edit":"编辑","view":"查看","back":"返回","next":"下一步","previous":"上一步","readMore":"阅读更多","learnMore":"了解更多","seeAll":"查看全部","tryNow":"立即尝试","startNow":"立即开始","comingSoon":"即将推出","importantNote":"重要提示","medicalDisclaimer":"本信息仅供教育目的，不旨在替代专业医疗建议。如有医疗问题，请咨询医疗专业人士。"},"healthGuidePage":{"title":"痛经健康指南","description":"您的完整痛经健康资源，从基础知识到高级管理策略，帮助您全面了解和管理经期健康。"},"teenHealth":{"title":"青少年经期健康专区","description":"专为青少年设计的经期健康指南，涵盖生理发育、情感支持和校园生活管理。","campusGuide":{"title":"校园生活指南","description":"在学校环境中管理经期的实用建议和策略。"},"developmentPain":{"title":"发育期疼痛管理","description":"了解青春期经期疼痛的特点和管理方法。"},"emotionalSupport":{"title":"情感支持","description":"应对经期情绪变化的心理健康指导。"},"communicationGuide":{"title":"沟通指南","description":"如何与家长、老师和朋友谈论经期话题。"}},"toolPage":{"backToTools":"返回工具列表","toolNotFound":"工具未找到","toolNotFoundDesc":"抱歉，我们找不到您要查找的工具。","medicalDisclaimer":"医疗免责声明","medicalDisclaimerText":"此工具仅供参考，不能替代专业医疗建议、诊断或治疗。如有健康问题，请咨询医疗专业人士。","categories":{"assessment":"评估工具","tracking":"追踪工具","constitutionAssessment":"体质评估","healthAssessment":"健康评估"},"difficulty":{"easy":"简单"},"estimatedTime":{"5to10min":"5-10分钟","2to3minDaily":"每日2-3分钟","5to8min":"5-8分钟","3to5min":"3-5分钟"}},"downloads":{"common":{"download":"查看文档","document":"文档","pages":"页"}},"painTracker":{"title":"疼痛追踪器","subtitle":"记录和追踪您的经期疼痛，了解疼痛模式，找到最有效的缓解方法","description":"通过详细记录疼痛信息，帮助您更好地管理经期健康","navigation":{"overview":"概览","addEntry":"添加记录","viewEntries":"查看记录","statistics":"统计分析","export":"导出数据"},"form":{"title":"疼痛记录","editTitle":"编辑疼痛记录","date":"日期","painLevel":"疼痛强度","duration":"持续时间","location":"疼痛位置","menstrualStatus":"月经状态","symptoms":"伴随症状","remedies":"缓解方法","effectiveness":"有效性评级","notes":"备注","optional":"可选","minutes":"分钟","save":"保存记录","cancel":"取消","saving":"保存中...","edit":"编辑记录","update":"更新记录","updating":"更新中...","notesPlaceholder":"记录任何额外的信息，如触发因素、情绪状态等..."},"entries":{"title":"疼痛记录","noEntries":"暂无疼痛记录","noEntriesDescription":"开始记录您的疼痛信息，以便更好地了解疼痛模式","addFirst":"添加第一条记录","totalEntries":"总记录数","lastEntry":"最近记录","edit":"编辑","delete":"删除","confirmDelete":"确认删除","deleteMessage":"确定要删除这条记录吗？此操作无法撤销。","deleteSuccess":"记录已删除","deleteError":"删除失败，请重试","painIntensity":"疼痛强度","duration":"持续时间","minutes":"分钟"},"statistics":{"title":"统计分析","overview":"概览","trends":"趋势分析","patterns":"疼痛模式","remedies":"缓解方法效果","totalEntries":"总记录数","averagePain":"平均疼痛强度","maxPain":"最高疼痛强度","minPain":"最低疼痛强度","trendDirection":"疼痛趋势","improving":"改善中","worsening":"恶化中","stable":"稳定","mostCommonSymptoms":"最常见症状","mostEffectiveRemedies":"最有效缓解方法","painFrequency":"疼痛强度分布","noData":"暂无数据","noDataDescription":"添加更多记录以查看统计分析","painLevelChart":"疼痛强度趋势","cyclePatterns":"月经周期疼痛模式","remedyEffectiveness":"缓解方法有效性","inDevelopment":"统计功能正在开发中..."},"export":{"title":"导出数据","description":"将您的疼痛记录导出为文件，便于备份或与医生分享","format":"导出格式","json":"JSON 格式","csv":"CSV 格式","pdf":"PDF 报告","dateRange":"日期范围","allData":"所有数据","lastMonth":"最近一个月","lastThreeMonths":"最近三个月","lastSixMonths":"最近六个月","customRange":"自定义范围","startDate":"开始日期","endDate":"结束日期","includeCharts":"包含图表","includeStatistics":"包含统计信息","export":"导出","exporting":"导出中...","exportSuccess":"导出成功","exportError":"导出失败，请重试","inDevelopment":"导出功能正在开发中..."},"insights":{"title":"健康洞察","painPatterns":"疼痛模式分析","recommendations":"个性化建议","trends":"趋势分析","alerts":"健康提醒","noInsights":"暂无洞察","noInsightsDescription":"记录更多数据以获得个性化健康洞察"},"settings":{"title":"设置","notifications":"通知设置","reminders":"记录提醒","privacy":"隐私设置","dataManagement":"数据管理","clearAllData":"清除所有数据","clearDataWarning":"此操作将永久删除所有疼痛记录，无法恢复","clearDataConfirm":"确认清除","clearDataSuccess":"数据已清除","clearDataError":"清除失败，请重试"},"messages":{"saveSuccess":"记录保存成功","saveError":"保存失败，请重试","updateSuccess":"记录更新成功","updateError":"更新失败，请重试","deleteSuccess":"记录删除成功","deleteError":"删除失败，请重试","confirmDelete":"确定要删除这条记录吗？此操作无法撤销。","loadError":"加载数据失败，请刷新页面","networkError":"网络错误，请检查连接","validationError":"请检查输入信息","duplicateDate":"该日期已有记录，请选择其他日期或编辑现有记录","assessmentComplete":"评估完成","assessmentCompleteDesc":"您的个性化建议已生成","assessmentFailed":"评估失败","assessmentFailedDesc":"无法生成评估结果，请重试","resultsSaved":"结果已保存","resultsSavedDesc":"您可以随时查看评估结果","close":"关闭"},"help":{"title":"使用帮助","gettingStarted":"开始使用","recordingPain":"记录疼痛","viewingData":"查看数据","exportingData":"导出数据","tips":"使用技巧","faq":"常见问题"},"assessment":{"title":"症状评估工具","subtitle":"通过专业问卷快速识别疼痛类型，为您提供精准的个性化建议。","start":{"title":"开始评估前","description":"请确保您在一个安静、私密的环境中，可以专心回答问题。这个评估将帮助您更好地了解自己的症状模式。","feature1":"专业的症状分析","feature2":"个性化建议","feature3":"科学的评估方法","feature4":"即时结果反馈","startButton":"开始评估","disclaimer":"此评估仅供参考，不能替代专业医疗诊断。"},"progress":{"questionOf":"第 {current} 题，共 {total} 题"},"navigation":{"previous":"上一题","next":"下一题","skip":"跳过","finish":"完成评估"},"result":{"title":"评估结果","yourScore":"您的得分","severity":"严重程度","summary":"结果摘要","recommendations":"建议方案","timeframe":"时间框架：","actionSteps":"行动步骤","retakeAssessment":"重新评估","saveResults":"保存结果","nextSteps":{"trackSymptoms":"","tryRecommendations":"","consultDoctor":""}},"severity":{"mild":"轻度","moderate":"中度","severe":"重度","emergency":"紧急"},"priority":{"high":"高优先级","medium":"中优先级","low":"低优先级"},"messages":{"assessmentComplete":"评估完成","assessmentCompleteDesc":"您的症状评估已完成，请查看结果和建议。","assessmentFailed":"评估失败","assessmentFailedDesc":"评估过程中出现错误，请重试。","resultsSaved":"结果已保存","resultsSavedDesc":"您的评估结果已保存到本地存储。"},"resultMessages":{"emergency":"您的症状较为严重，建议尽快咨询医疗专业人士。","emergencySummary":"评估显示您可能需要专业医疗关注。","severe":"您的症状比较严重，建议采取综合管理策略。","severeSummary":"您的症状需要积极的管理和可能的医疗干预。","moderate":"您有中等程度的症状，可以通过多种方法进行管理。","moderateSummary":"您的症状是可以管理的，建议采用多种缓解策略。","mild":"您的症状相对较轻，通过简单的自我护理就能很好地管理。","mildSummary":"您的症状较轻，可以通过生活方式调整来改善。"},"recommendations":{"emergencyMedical":{"title":"建议立即就医","description":"您的症状可能需要专业医疗评估和治疗","timeframe":"立即","actionSteps":["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"]},"painManagement":{"title":"疼痛管理策略","description":"多种方法可以帮助缓解经期疼痛","timeframe":"立即可用","actionSteps":["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"]},"lifestyleChanges":{"title":"生活方式调整","description":"长期的生活方式改变可以显著改善症状","timeframe":"2-3个月见效","actionSteps":["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"]},"selfcarePractices":{"title":"自我护理实践","description":"日常的自我护理可以帮助您更好地管理症状","timeframe":"持续进行","actionSteps":["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"]}}}},"homePageContent":{"searchPlaceholder":"\uD83D\uDD0D 快速搜索痛经解决方案...","searchTips":"\uD83D\uDCA1 试试搜索\\"5分钟缓解\\"、\\"热敷\\"、\\"前列腺素\\"","statistics":{"title":"数据说话，效果可见","description":"基于真实用户反馈和科学研究的数据统计","improvement":"用户症状改善","users":"10万+","totalUsers":"累计用户","support":"在线支持","articles":"专业文章"},"healthGuide":{"title":"痛经健康指南","description":"全面的痛经健康知识体系，从基础理解到高级管理策略，助您掌握经期健康。"},"smartTools":{"title":"智能健康工具","description":"专业的评估和追踪工具，帮助您更好地了解和管理经期健康"},"medicalDisclaimer":"医疗免责声明"}}')}},e=>{var t=e(e.s=4468);(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES).middleware_middleware=t}]);
//# sourceMappingURL=middleware.js.map