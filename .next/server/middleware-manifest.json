{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(zh|en))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/(zh|en)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next|_vercel|.*\\..*).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!_next|_vercel|.*\\..*).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "P0ZJ3CVcK51FHdrDTqjvV", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1ezboETHwTF92AjbkvBzJaygl3PW0Na6VIvlojsrNFE=", "__NEXT_PREVIEW_MODE_ID": "7dc450d61661f4f382703aad4b936701", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "043cacdc35cfb925508e9ae35e404b2010e70affcc60bfd5bf4d93b7b1acf099", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cdb4cd829b7740ff5c1a1d7a95d4cd9ac1799aafd6c126408e1790d6dbba1446"}}}, "functions": {"/icon/route": {"files": ["server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/icon/route.js"], "name": "app/icon/route", "page": "/icon/route", "matchers": [{"regexp": "^/icon$", "originalSource": "/icon"}], "wasm": [{"name": "wasm_77d9faebf7af9e421806970ce10a58e9d83116d7", "filePath": "server/edge-chunks/wasm_77d9faebf7af9e421806970ce10a58e9d83116d7.wasm"}, {"name": "wasm_ef4866ecae192fd87727067cf2c0c0cf9fb8b020", "filePath": "server/edge-chunks/wasm_ef4866ecae192fd87727067cf2c0c0cf9fb8b020.wasm"}], "assets": [{"name": "noto-sans-v27-latin-regular.5dda3fca77107598.ttf", "filePath": "server/edge-chunks/asset_noto-sans-v27-latin-regular.5dda3fca77107598.ttf"}], "env": {"__NEXT_BUILD_ID": "P0ZJ3CVcK51FHdrDTqjvV", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "1ezboETHwTF92AjbkvBzJaygl3PW0Na6VIvlojsrNFE=", "__NEXT_PREVIEW_MODE_ID": "7dc450d61661f4f382703aad4b936701", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "043cacdc35cfb925508e9ae35e404b2010e70affcc60bfd5bf4d93b7b1acf099", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cdb4cd829b7740ff5c1a1d7a95d4cd9ac1799aafd6c126408e1790d6dbba1446"}}}, "sortedMiddleware": ["/"]}